---
title: 数据持久化
sidebar_label: 数据持久化
---

## 介绍

部分场景下，我们希望能够对小程序一部分数据状态进行持久化存储。

简单来说，持久化就是将数据保存到可永久保存的存储设备中。结合一个例子来看一下实际的应用。

以示例应用的 [calories](./../../samples/app/calories.md) 举例，这个小程序可以读取当天的卡路里消耗，并且计算卡路里数值与不同食物间的关系，提供了 10 种食物供用户选择，默认选择巧克力。如果用户想换成汉堡，需要去选择页面进行操作，假设程序没有做持久化处理，用户退出小程序再次进入该小程序后，所选择的食物仍然是巧克力。如果做了持久化处理，则在每次进入小程序的时候，都能获取到之前保存的应用状态，所选择的食物就成了汉堡。

## 思路

在小程序中实现数据持久化，可以借助文件系统相关 API，即读写文件来实现。但这样的操作较为繁琐，可以借助 [`localStorage` API](../../reference/device-app-api/newAPI/storage/localStorage.mdx) 来实现。

```js title="page.js"
import { LocalStorage } from '@zos/storage'

const localStorage = new LocalStorage()

Page({
  state: {
    data: null,
    storage: localStorage
  },
  build() {
    this.state.data = localStorage.getItem('state')
    // ...
  },
  onDestroy() {
    localStorage.setItem('state', this.state.data)
  }
})
```
