# setLaunchAppTimeout

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册一个定时器，定时唤起小程序，在此期间如果设备重启，则定时器会失效。

## 类型

```ts
function setLaunchAppTimeout(option: Option): Result
```

## 参数

### Option

| 属性   | 类型                            | 必填 | 默认值         | 说明                                                                                                                         | API_LEVEL |
| ------ | ------------------------------- | ---- | -------------- | ---------------------------------------------------------------------------------------------------------------------------- | --------- |
| appId  | <code>number</code>             | 是   | -              | 小程序 ID                                                                                                                    | 2.0       |
| url    | <code>string</code>             | 是   | -              | 页面路径                                                                                                                     | 2.0       |
| utc    | <code>number</code>             | 否   | -              | utc 时间戳（毫秒），优先级高于 `delay`，当与 `delay` 字段同时设置的时候，只有 `utc` 字段生效                                 | 2.0       |
| delay  | <code>number</code>             | 否   | <code>0</code> | 等待时间（毫秒）                                                                                                             | 3.0       |
| params | <code>string&#124;object</code> | 否   | -              | 传递给 app.js 生命周期 `onCreate` 中的参数，支持字符串或者标准 JSON 对象。如果传递标准 JSON 对象，该方法内部会将其转为字符串 | 2.0       |

### Result

| 类型                | 说明                                                                    |
| ------------------- | ----------------------------------------------------------------------- |
| <code>number</code> | 表示定时器的编号，这个值可以传递给 `clearLaunchAppTimeout` 来取消定时器 |

## 代码示例

```js
import { setLaunchAppTimeout, clearLaunchAppTimeout } from '@zos/router'

const timeoutId = setLaunchAppTimeout({
  url: 'pages/js_widget_sample',
  appId: 1000001,
  delay: 1000,
})

clearLaunchAppTimeout({
  timeoutId,
})
```
