# emitCustomSystemEvent

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

小程序可以自定义系统事件，并可以主动派发该自定义系统事件。

## 类型

```ts
function emitCustomSystemEvent(option: Option): void
```

## 参数

### Option

| 属性       | 类型                | 必填 | 默认值 | 说明                                                                             | API_LEVEL |
| ---------- | ------------------- | ---- | ------ | -------------------------------------------------------------------------------- | --------- |
| eventName  | <code>string</code> | 是   | -      | 自定义事件名称，需要满足 `event:customize.${event}` 的命名约定                   | 3.0       |
| eventParam | <code>string</code> | 是   | -      | 自定义事件参数，这个参数会传递到 `AppService` 构造函数的 `onInit` 生命周期函数中 | 3.0       |

## 代码示例

```js
import { emitCustomSystemEvent } from '@zos/app'

emitCustomSystemEvent({
  eventName: 'event:customize.test',
  eventParam: 'eventName=event:customize.test&type=0',
})
```
