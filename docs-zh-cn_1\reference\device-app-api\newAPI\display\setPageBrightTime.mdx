# setPageBrightTime

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

设置当前页面屏幕亮屏时间，这个设置随页面销毁会做重置。

## 类型

```ts
function setPageBrightTime(option: Option): Result
```

## 参数

### Option

| 属性       | 类型                | 必填 | 默认值             | 说明                                       | API_LEVEL |
| ---------- | ------------------- | ---- | ------------------ | ------------------------------------------ | --------- |
| brightTime | <code>number</code> | 否   | <code>10000</code> | 亮屏时间（毫秒），范围 [1000 - 2147483000] | 2.0       |

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { setPageBrightTime } from '@zos/display'

const result = setPageBrightTime({
  brightTime: 60000,
})

if (result === 0) {
  console.log('setPageBrightTime success')
}
```
