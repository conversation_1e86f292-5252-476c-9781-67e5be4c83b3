---
sidebar_label: 表盘
---

# 表盘

表盘是智能可穿戴设备核心体验要素也是用户最常使用的功能。表盘既有传统手表的时间显示能力，同时又具备了不同种类数据展示的功能，表盘的设计需要适应用户的个性化需求，并匹配不同场景。  

表盘设计和制作文档请参考:[表盘配置 | ZeppOS Developers Documentation](../../watchface/app-json.md)

## 设计原则  

**识别性**：数据展示一目了然，视觉层级有区分度，色彩组合协调可辨识，从而提高用户读取信息的效率与准确率；  

**多样性**：从表盘功能，设计风格，数据展示等不同维度保证表盘的多样性，给用户更多的选择；  

**交互性**：善用智能穿戴设备的交互特性，结合表冠，陀螺仪，手势等交互提高表盘的可玩性；  

## 构成  

### 时间  

**时分秒**  

- 数字、指针。
- 数字切图的间隔支持输入负数，来实现层叠效果。
- 手表指针显示顺序是秒针在最上，中间层是分针，最下层是时针。  

![Design](/img/design/customization_1.png)

**上午下午**
- 文字。

**星期**
- 文字、指针、进度。  

![Design](/img/design/customization_2.png)


**年月日**
- 数字、指针、进度。
- 如表盘显示年，必须年/月/日一起，显示不支持年分开显示。  

![Design](/img/design/customization_3.png)

农历
- 文字。
- 可单独配置节日/节气坐标。
[图片]

### 运动

**步数**  

- 图标、数字、指针、进度。
- 步数：最大5个字符，显示范例：“00000”。
- 目标步数：最大5个字符，显示范例：“00000”。
- 空状态：“0”。  

![Design](/img/design/customization_4.png)

**距离**  

- 图标、数字。
- 距离数字：最大 4 个字符，显示范例 ：“0.00”，“00.0”。
- 空状态：“0”。  

![Design](/img/design/customization_5.png)

**卡路里**  

- 图标、数字、指针、进度。
- 消耗：最大 4 个字符，显示范例 ：“0000”。
- 空状态：“0”。  

![Design](/img/design/customization_6.png)

**站立**  

- 图标、数字、指针、进度。
- 当前站立：最大 2 个字符，显示范例：“00”。
- 站立目标： 12 小时 （固定）。
- 空状态：“0”。  

![Design](/img/design/customization_7.png)

### 健康
**心率**
- 由图标、数字、指针、进度组成。
- 心率：最大3个字符，显示范例：“000”。
- 空状态：“- -”。  

![Design](/img/design/customization_8.png)

**PAI** 
- 图标、数字、指针、进度。
- PAI：最大3个字符，显示范例：“000”。
- 空状态：“0”  

![Design](/img/design/customization_9.png)

**体重**
- 图标、数字、指针、进度。
- 体重：最大3个字符，显示范例：“000”。
- 空状态：“--”   

![Design](/img/design/customization_10.png)

**运动负荷**
- 图标、数字、指针、进度。
- 运动负荷：最大4个字符，显示范例：“0000”。
- 空状态：“--”  

![Design](/img/design/customization_11.png)

**Readiness**
- 图标、数字、指针、进度。
- Readiness：最大3个字符，显示范例：“000”。
- 空状态：“--”  

![Design](/img/design/customization_12.png)

### 天气
**温度**
- 图标、数字、指针、进度。
- 实时温度显示：最大5个字符，显示范例：“-000°”。
- 最低温度显示：最大5个字符，显示范例：“-000°”。
- 最高温度显示：最大5个字符，显示范例：“-000°”。
- 空状态：“- -”。  

![Design](/img/design/customization_13.png)

**紫外线**
- 图标、数字、指针、进度。
- 紫外线等级：最大2个字符，显示范例：“00”。
- 空状态：“- -”。  

![Design](/img/design/customization_14.png)

**湿度**
- 图标、数字、指针、进度。
- 湿度：最大4个字符，显示范例：“000%”。
- 空状态：“- -”。  

![Design](/img/design/customization_15.png)

**空气质量**
- 图标、数字、指针、进度。
- 空气质量：最大3个字符，显示范例：“000”。
- 非中国地区无法展示空气质量，替换成湿度。
- 空状态：“- -”。  

![Design](/img/design/customization_16.png)

**日出日落**
- 图标、数字、指针、进度。
- 当前时间：最大5个字符，显示范例：“00:00”。
- 日出时间：最大5个字符，显示范例：“00:00”。
- 日落时间：最大5个字符，显示范例：“00:00”。
- 空状态：“- -”。  

![Design](/img/design/customization_17.png)

**月相**
- 7/13/30张月相图片。  

![Design](/img/design/customization_18.png)

**气压**
- 图标、数字、指针、进度。
- 气压：最大5个字符，显示范例：“00000”。
- 空状态：“- -”。  

![Design](/img/design/customization_19.png)

### 系统
**电量**
- 图标、数字、指针、进度。
- 电量：最大4个字符，显示范例：“100%”。  

![Design](/img/design/customization_20.png)  

### 系统状态
**图标**
- 勿扰、蓝牙、锁屏、闹钟，四个状态都必须设计开关状态，来表示功能是否被开启。  

![Design](/img/design/customization_21.png)

**指南针**
显示当前的手表指向方位  

显示当前角度和方位  

![Design](/img/design/customization_22.png)

**闹钟**
- 图标
- 下一个闹钟时间：按照系统 12/24 小时计，显示范例 ：“08:00”，“12:00”。
- 空状态：“--”。  

![Design](/img/design/customization_23.png)

**呼吸**
- 图标  

![Design](/img/design/customization_24.png)

**倒计时**
- 由图标、数字、指针、进度组成。
- 显示当前进行的倒计时，显示范例：“00:36”。
- 空状态：“- -”。  

![Design](/img/design/customization_25.png)

## 视觉规范
- 时间信息是每个表盘的必要元素，可选择指针时间、数字时间或者指针和数字相结合的的方式来进行设计。
- 最小字号为22px，表盘元素的线条粗细必须≥1px。
- 边缘留出 2px 安全距离。顶部和底部留出状态点和离线语音的展示区域，不摆放重要信息。  

![Design](/img/design/customization_26.png)  

- 多语言支持：表盘必需支持英文、繁体、简体三种语言，当手表切换到表盘不支持的语言时，均展示英文。
- 表盘预览图的时间数据必须显示 10:09，其余数据需要符合规范和基本认知。