---
title: TEXT
sidebar_label: TEXT 文本
---

![text_sample](/img/api/text_sample.jpg)

文本控件用于展示文本。支持设置文本大小、颜色、对齐方式。

## 创建 UI 控件

```js
const text = hmUI.createWidget(hmUI.widget.TEXT, Param)
```

## 类型

## Param: object

| 属性       | 备注                                             | 是否必须 | 类型         |
| ---------- | ------------------------------------------------ | -------- | ------------ |
| x          | 控件 x 坐标                                      | 是       | `number`     |
| y          | 控件 y 坐标                                      | 是       | `number`     |
| w          | 控件显示宽度                                     | 是       | `number`     |
| h          | 控件显示高度                                     | 是       | `number`     |
| color      | 文本颜色                                         | 否       | `number`     |
| align_h    | 横轴对齐方式（值见 ALIGN）                       | 否       | `ALIGN`      |
| align_v    | 竖轴对齐方式（值见 ALIGN）                       | 否       | `ALIGN`      |
| text       | 文本                                             | 否       | `string`     |
| text_size  | 字体大小                                         | 否       | `number`     |
| text_style | 文本超长处理方式 默认为跑马灯（值见 TEXT_STYLE） | 否       | `TEXT_STYLE` |
| line_space | 行间距                                           | 否       | `number`     |
| char_space | 字符间距                                         | 否       | `number`     |

### ALIGN 对齐方式

| 值                  | 说明        |
| ------------------- | ----------- |
| hmUI.align.LEFT     | 横轴-左对齐 |
| hmUI.align.RIGHT    | 横轴-右对齐 |
| hmUI.align.CENTER_H | 横轴-居中   |
| hmUI.align.TOP      | 竖轴-最上端 |
| hmUI.align.BOTTOM   | 竖轴-最底端 |
| hmUI.align.CENTER_V | 竖轴-居中   |

### TEXT_STYLE 文本排版

| 值                        | 说明                |
| ------------------------- | ------------------- |
| hmUI.text_style.ELLIPSIS  | 单行溢出字符显示... |
| hmUI.text_style.NONE      | 跑马灯              |

## 代码示例

```js
Page({
  build() {
    const text = hmUI.createWidget(hmUI.widget.TEXT, {
      x: 96,
      y: 120,
      w: 288,
      h: 46,
      color: 0xffffff,
      text_size: 36,
      align_h: hmUI.align.CENTER_H,
      align_v: hmUI.align.CENTER_V,
      text_style: hmUI.text_style.NONE,
      text: 'HELLO ZEPPOS'
    })

    text.addEventListener(hmUI.event.CLICK_DOWN, (info) => {
      text.setProperty(hmUI.prop.MORE, {
        y: 200
      })
    })
  }
})
```
