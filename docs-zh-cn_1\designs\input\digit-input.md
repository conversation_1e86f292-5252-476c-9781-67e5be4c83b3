---
sidebar_label: 数字输入
---

# 数字输入   

数字输入为用户提供了一种可视化的方式去进行数字相关信息录入。  

## 设计目标  

为用户提供简单有效的数字信息输入形式，帮助用户高效的输入完成任务。
用户进行操作时，提供清晰的视觉反馈，为用户输入提供引导帮助。  

## 类型  

- 解锁控件
- 拨号控件
- 计算器工具  

![Design](/img/design/input_14.png)  

![Design](/img/design/input_15.png)

## 使用规则  

解锁控件在检测到穿戴设备离腕状态下开启，点击数字输入，屏幕显示输入数字500ms后隐藏字符。  

![Design](/img/design/input_16.png)

## 视觉规范  

- 拨号/解锁控件数字字符区点击时字符加底色1.26比例放大，点击结束恢复正常状态。点击态底色色值：#FFFFFF  20%   

![Design](/img/design/input_17.png)   

- 计算器工具圆形按钮区点按时按键1.26比例放大，点按结束恢复正常。  

![Design](/img/design/input_18.png)  

>点按态底色色值对应变化关系：
>
>①数字按钮点按状态下色值变化为 #333333 — #666666   
>
>②符号按钮点按状态下色值变化为 #666666 — #999999
>
>③等于按钮点按状态下色值变化为#0986D4 — #3A9EDD       

- 计算器工具符号按钮点击确定后点亮状态告知用户当前所使用的计算方式。  

![Design](/img/design/input_19.png)