---
title: 选择合适的 API 版本
sidebar_label: 选择合适的 API 版本
---

Zepp OS 2.0 新特性发布后，很多开发者咨询 API 版本选择的问题，Zepp OS 团队梳理了一张图供开发者参考。

图片较大，建议右键新标签页中打开后查看大图。

![choose_api_version](/img/docs/guides/version_info/choose_api_version_zh.jpg)

对图中出现名词，概念解释：

相关概念|说明
---|---
api v1|Zepp OS 1.0 API
api v2|Zepp OS 2.0 API
搭载 Zepp OS 1.0 设备|在 [设备基本信息](../../reference/related-resources/device-list.mdx) 可以进行查询
platform|是 [app.json](../../reference/app-json.mdx) 中的一个字段，用于配置小程序的运行平台
