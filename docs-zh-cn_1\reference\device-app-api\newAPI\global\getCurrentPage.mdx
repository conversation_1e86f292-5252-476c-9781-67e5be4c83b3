# getCurrentPage

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

获取 page 实例对象。

## 类型

```ts
function getCurrentPage(): Result
```

## 参数

### Result

| 属性      | 类型                 | 说明          | API_LEVEL |
| --------- | -------------------- | ------------- | --------- |
| \_options | <code>Options</code> | page 实例属性 | 2.0       |

### Options

| 属性  | 类型                | 必填 | 默认值 | 说明                        | API_LEVEL |
| ----- | ------------------- | ---- | ------ | --------------------------- | --------- |
| state | <code>object</code> | 否   | -      | page 实例上的挂载的数据对象 | 2.0       |

## 代码示例

```js title="page.js"
Page({
  state: {
    text: 'Hello Zepp OS',
  },
  onInit() {
    console.log('onInit')
  },
  build() {
    console.log('build')
    console.log(this.state.text)
  },
})

const page = getCurrentPage()
console.log(page._options.state.text)
```
