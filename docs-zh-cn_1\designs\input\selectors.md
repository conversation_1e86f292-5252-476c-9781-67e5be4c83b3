---
sidebar_label: 选择器
---

# 选择器

## 数据选择器

数据选择器组件是指为用户提供单项或多项参数的选项集合控件。  

### 设计原则  

**合理**  

- 合理提供默认选项：提供合理的默认选项能传达正确信息让用户减少操作次数，提升效率。
- 合理的排列顺序：选项排序按照产品功能选择按照逻辑排序或者用户体验排序，方便用户进行快速选择。  

**简单**  

- 描述简单：选择器内文本（标题、描述文本）简单明了，便于用户快速理解；选项单位请使用国际通用缩写。
- 选项简单：由于智能穿戴屏幕大小限制，选项过多或者选项本身复杂难理解请采用其他操作形式；建议选择器选项不超过3行，尽量不使用需要文本描述选项的选择器。  

![Design](/img/design/input_1.png)

### 类型  

数字选择器：由数字选项组成的选择器  

![Design](/img/design/input_2.png)  

多项组合选择器：由数字和文字选项组成的选择器  

![Design](/img/design/input_3.png)

### 使用规则

- 点击激活要设置的项，通过上下滑动或旋转表冠纵向切换参数值进行设定。直接滑动同样可以激活纵向参数的设定。  

![Design](/img/design/input_4.png)
![Design](/img/design/input_5.png)
>①默认状态  ②选中状态

- 文字类型选项切换方式同数字类型切换，通过上下滑动或旋转表冠纵向切换参数值进行设定。直接滑动同样可以激活纵向参数的设定。  

![Design](/img/design/input_6.png)  

- 选项单位请使用国际通用缩写。  

![Design](/img/design/input_7.png)

### 视觉规范  

- 控件位置根据上下板块自适应居中。  

- 选项数字字体：DIN1451    色值名称：默认 color_text_title    选中 color_sys_key  

![Design](/img/design/input_8.png)

![Design](/img/design/input_9.png)

## 页面选择器  

页面选择器是指为用户提供页面选择的控件。  

![Design](/img/design/input_10.png)

### 使用规则  

用户通过横向滑动或旋转表冠切换页面，点击确定当前页面。例如：表盘编辑。  

![Design](/img/design/input_11.png)

### 视觉规范  

- 滚动页面➀外描边2px，色值名称：color_sys_item bg  

![Design](/img/design/input_12.png)  

- 方屏设备与原屏设备滚动页面间隔相同  

![Design](/img/design/input_13.png)