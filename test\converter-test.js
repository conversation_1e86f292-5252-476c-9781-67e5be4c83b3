// 进制转换器功能测试
const CHARS = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

// 转换为十进制
function toDecimal(value, fromBase) {
  let result = 0
  for (let i = 0; i < value.length; i++) {
    const digit = CHARS.indexOf(value[i].toLowerCase())
    if (digit >= fromBase) return NaN
    result = result * fromBase + digit
  }
  return result
}

// 从十进制转换
function fromDecimal(decimal, toBase) {
  if (decimal === 0) return '0'
  let result = ''
  while (decimal > 0) {
    result = CHARS[decimal % toBase] + result
    decimal = Math.floor(decimal / toBase)
  }
  return result
}

// 测试用例
const testCases = [
  { input: '10', fromBase: 10, expected: { 2: '1010', 8: '12', 16: 'a' } },
  { input: 'ff', fromBase: 16, expected: { 2: '11111111', 8: '377', 10: '255' } },
  { input: '1010', fromBase: 2, expected: { 8: '12', 10: '10', 16: 'a' } },
  { input: '123', fromBase: 10, expected: { 2: '1111011', 8: '173', 16: '7b' } }
]

console.log('🧪 进制转换器功能测试')
console.log('========================')

testCases.forEach((testCase, index) => {
  console.log(`\n测试 ${index + 1}: ${testCase.input} (${testCase.fromBase}进制)`)
  
  const decimal = toDecimal(testCase.input, testCase.fromBase)
  if (isNaN(decimal)) {
    console.log('❌ 输入无效')
    return
  }
  
  console.log(`十进制: ${decimal}`)
  
  Object.entries(testCase.expected).forEach(([base, expected]) => {
    const result = fromDecimal(decimal, parseInt(base))
    const match = result.toLowerCase() === expected.toLowerCase()
    console.log(`${match ? '✅' : '❌'} ${base}进制: ${result} ${match ? '' : `(期望: ${expected})`}`)
  })
})

console.log('\n🎯 测试完成！')

// 测试大数值
console.log('\n🔢 大数值测试:')
const bigNumber = toDecimal('ZZZZ', 62)
console.log(`ZZZZ (62进制) = ${bigNumber} (十进制)`)
console.log(`${bigNumber} (十进制) = ${fromDecimal(bigNumber, 2)} (二进制)`)

// 测试边界情况
console.log('\n🚨 边界情况测试:')
console.log(`空字符串: ${toDecimal('', 10)}`)
console.log(`0: ${fromDecimal(0, 16)}`)
console.log(`无效字符 (G in 16进制): ${toDecimal('G', 16)}`)
