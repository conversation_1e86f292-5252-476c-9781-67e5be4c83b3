# cancel

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

取消设置的定时器，如果定时器设置了持久化，同时取消持久化。

:::info
权限代码： `device:os.alarm`
:::

## 类型

```ts
function cancel(option: Option): Result
```

### 简化调用方式

```ts
function cancel(id: number): Result
```

## 参数

### Option

| 属性 | 类型                | 必填 | 默认值 | 说明                         | API_LEVEL |
| ---- | ------------------- | ---- | ------ | ---------------------------- | --------- |
| id   | <code>number</code> | 是   | -      | 定时器 ID，由 `set` 方法返回 | 3.0       |

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { cancel } from '@zos/alarm'

cancel(id)
```
