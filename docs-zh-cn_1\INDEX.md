# Zepp OS 中文开发者文档索引

本文档集合包含了 Zepp OS 最新版本（v3/current）的完整中文开发者文档，共计 **506** 个文档文件。

## 文档概述

- **版本**: Current (v3) - 最新版本
- **语言**: 简体中文
- **文档总数**: 506 个文件
- **最后更新**: 2025-09-20

## 文档结构

### 1. 项目介绍
- `README-cn.md` - Zepp OS 开发者文档项目介绍

### 2. 入门指南 (intro/)
- `intro.mdx` - Zepp OS 开发文档介绍
- `intro/using.mdx` - 使用文档
- `intro/version.mdx` - 版本信息

### 3. 开发指南 (guides/)
#### 3.1 快速开始
- `guides/quick-start.mdx` - 快速上手指南

#### 3.2 架构 (guides/architecture/)
- `guides/architecture/folder-structure.mdx` - 目录结构
- `guides/architecture/life-cycle.mdx` - 生命周期

#### 3.3 最佳实践 (guides/best-practice/)
- `guides/best-practice/i18n.mdx` - 国际化
- `guides/best-practice/multi-screen-adaption.mdx` - 多屏适配
- `guides/best-practice/code-organization.mdx` - 代码组织

#### 3.4 框架介绍 (guides/framework/)
- 设备应用 API
- 伴生服务 API
- 设置应用 API

#### 3.5 工具 (guides/tools/)
- Zeus CLI 工具
- 模拟器使用
- 调试工具

#### 3.6 版本信息 (guides/version-info/)
- API 版本升级指南
- 新特性介绍

#### 3.7 常见问题 (guides/faq/)
- 开发环境问题
- 模拟器问题
- 调试问题

### 4. API 参考 (reference/)
#### 4.1 应用配置
- `reference/app-json.mdx` - 应用配置文件

#### 4.2 设备应用 API (reference/device-app-api/)
- UI 组件 API
- 传感器 API
- 系统 API
- 文件系统 API

#### 4.3 伴生服务 API (reference/side-service-api/)
- 网络请求 API
- 数据存储 API

#### 4.4 设置应用 API (reference/app-settings-api/)
- 设置界面组件

#### 4.5 相关资源 (reference/related-resources/)
- 设备列表
- 语言映射
- 错误码

### 5. 示例代码 (samples/)
#### 5.1 小程序示例 (samples/app/)
- Calories - 卡路里计算器
- ToDoList - 待办事项
- FetchAPI - 网络请求示例
- Showcase - 功能展示

#### 5.2 表盘示例 (samples/watchface/)
- 色彩时间 - 彩色时间表盘
- 篮球 - 运动主题表盘
- 计时器 - 计时功能表盘

### 6. 表盘开发 (watchface/)
- `watchface/watchface-quick-start.mdx` - 表盘快速开始
- `watchface/specification.md` - 表盘规范
- `watchface/design-resources.md` - 设计资源
- `watchface/app-json.md` - 表盘配置
- `watchface/api/` - 表盘 API 文档

### 7. 设计规范 (designs/)
#### 7.1 设计概念 (designs/concept/)
- 设计原则
- 关键词
- 价值观

#### 7.2 界面元素 (designs/elements/)
- 文本规范
- 进度条
- 图表
- 加载动画

#### 7.3 交互设计 (designs/interaction/)
- 触屏控制
- 物理按键
- 数字表冠

#### 7.4 视觉设计 (designs/visual/)
- 动画效果
- 颜色规范
- 字体规范

#### 7.5 国际化 (designs/internationalization/)
- 界面布局
- 语言支持
- 区域标准

### 8. 分发指南 (distribute/)
- `distribute/index.md` - 应用分发
- `distribute/watchface.md` - 表盘分发

## 使用建议

### 对于 AI 开发程序
1. **优先阅读**: `intro.mdx`, `guides/quick-start.mdx`
2. **架构理解**: `guides/architecture/` 目录下的所有文档
3. **API 参考**: `reference/` 目录包含完整的 API 文档
4. **示例学习**: `samples/` 目录提供实际代码示例
5. **问题解决**: `guides/faq/` 目录包含常见问题解答

### 文档特点
- ✅ 最新版本 (v3/current)
- ✅ 完整的中文文档
- ✅ 包含代码示例
- ✅ 详细的 API 参考
- ✅ 设计规范指导
- ✅ 常见问题解答

## 技术栈
- **平台**: Zepp OS (智能手表操作系统)
- **开发语言**: JavaScript
- **框架**: Zeus Mini-Program Framework
- **工具**: Zeus CLI, 模拟器
- **设计**: Figma 组件库

## 更新说明
本文档集合基于 Zepp OS 官方文档仓库的最新版本整理，确保内容的准确性和时效性。如需了解历史版本或英文文档，请参考原始仓库。
