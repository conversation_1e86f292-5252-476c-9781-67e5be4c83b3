---
title: CIRCLE
sidebar_label: CIRCLE 圆形
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../../guides/framework/device/compatibility.md)。

![circle_sample](/img/api/circle_sample.jpg)

绘制一个圆形，支持颜色、透明度等属性。

:::caution
CIRCLE 控件暂时不支持通过 `addEventListener` 注册事件
:::

## 创建 UI 控件

```js
import { createWidget, widget } from '@zos/ui'

const circle = createWidget(widget.CIRCLE, Param)
```

## 类型

### Param: object

| 属性     | 备注                      | 是否必须 | 类型     |
| -------- | ------------------------- | -------- | -------- |
| center_x | 圆心 x 坐标               | 是       | `number` |
| center_y | 圆心 y 坐标               | 是       | `number` |
| radius   | 半径                      | 是       | `number` |
| color    | 颜色 16 进制值            | 是       | `number` |
| alpha    | 透明度[0-255]，0 为全透明 | 否       | `number` |

## 属性访问支持列表

| 属性名      | setProperty | getProperty | [setter](../gettersetter.mdx) | [getter](../gettersetter.mdx) |
|-------------|-------------|-------------|-------------------------------|-------------------------------|
| center_x   | Y           | Y           | Y                             | Y                             |
| center_y   | Y           | Y           | Y                             | Y                             |
| radius     | Y           | Y           | Y                             | Y                             |
| color      | Y           | Y           | Y                             | Y                             |
| alpha      | Y           | Y           | Y                             | Y                             |

## 代码示例

```js
import { createWidget, widget } from '@zos/ui'

Page({
  build() {
    const circle = createWidget(widget.CIRCLE, {
      center_x: 240,
      center_y: 240,
      radius: 120,
      color: 0xfc6950,
      alpha: 200
    })
  }
})
```
