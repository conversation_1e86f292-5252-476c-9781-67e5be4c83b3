---
sidebar_label: 开关
---

# 开关  

开关是用于切换单个选项/卡片状态的控件。  

![Design](/img/design/switches_1.png)  

## 使用规则  

当用户切换「开关」按钮将直接触发状态改变。  

![Design](/img/design/switches_2.png)  

## 视觉规范  

- 开关对应四种状态 ：  
①开启：开关开启，用户可以手动切换开启和关闭的状态  
②关闭：开关关闭，用户可以手动切换开启和关闭的状态  
③开启（禁用）：开关开启，用户此时不能手动切换到关闭状态  
④关闭（禁用）：开关关闭，用不此时不能手动切换到开启状态  

![Design](/img/design/switches_3.png)  

- 开关页面布局统一放置在列表/卡片右侧   

![Design](/img/design/switches_4.png)
