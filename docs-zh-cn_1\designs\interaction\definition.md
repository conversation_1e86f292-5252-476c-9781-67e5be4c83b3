---
sidebar_label: 手表 keyType 定义
---

# 手表 keyType 定义

通过此字段可以确定手表的按键定义，包含如下信息：

- 按键数量
- 是否有表冠
- 模式类型区分（标准/运动）

## 命名规则

```javascript
{按键模式}_{按键数量} // {是否有表冠，1代表有，0没有}
```

比如 sport_21 代表「按键数量 2」、「有表冠」、「按键模式：运动」

按键模式：标准Normal、运动Sport

按键数量 ：1(单键)、2(双键)、4(四键)

是否有表冠：0(无)、1(有)

| **名称**               | **类型：英文** |
| ---------------------- | -------------- |
| 单键表/有表冠/标准模式 | normal_11      |
| 单键表/无表冠/标准模式 | normal_10      |
| 双键表/有表冠/标准模式 | normal_21      |
| 双键表/无表冠/标准模式 | normal_20      |
| 双键表/有表冠/运动模式 | sport_21       |
| 双键表/无表冠/运动模式 | sport_20       |
| 四键表/无表冠/运动模式 | sport_40       |

## 使用规则

- 运动表默认按键模式：运动 / Sport
- 非运动表默认按键模式：标准 / Normal
- 后续新增按键模式，因此双键表会存在 2 种按键模式都支持的情况
