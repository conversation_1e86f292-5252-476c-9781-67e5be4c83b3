# setScreenOff

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

设置屏幕息屏。

## 类型

```ts
function setScreenOff(): Result
```

## 参数

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { setScreenOff } from '@zos/display'

const result = setScreenOff()

if (result === 0) {
  console.log('setScreenOff success')
}
```
