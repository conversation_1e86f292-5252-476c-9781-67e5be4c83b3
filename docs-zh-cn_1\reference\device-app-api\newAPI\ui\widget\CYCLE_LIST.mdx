---
title: CYCLE_LIST
sidebar_label: CYCLE_LIST 循环列表（图片）
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../../guides/framework/device/compatibility.md)。

![cycle_list_sample](/img/api/cycle_list_sample.jpg)

创建一个可以循环滚动的列表，每个 item 可以设置为一张图片。

## 创建 UI 控件

```js
import { createWidget, widget } from '@zos/ui'

const cycleList = createWidget(widget.CYCLE_LIST, Param)
```

## 类型

### Param: object

| 属性          | 说明              | 是否必须 | 类型            |
| --------------- | ----------------- | -------- | --------------- |
| item_height     | item 高度         | 是       | `number`        |
| item_bg_color   | item 背景色       | 是       | `number`        |
| x               | 控件 x 坐标 | 是       | `number`        |
| y               | 控件 y 坐标       | 是       | `number`        |
| w               | 控件显示宽度      | 是       | `number`        |
| h               | 控件显示高度      | 是       | `number`        |
| data_array      | 数据数组          | 是       | `array`         |
| data_size       | 数组长度          | 是       | `number`        |
| item_click_func | 点击 item 的回调  | 否       | `ItemClickFunc` |
| item_focus_change_func | item 焦点态回调  | 否       | `ItemFocusChangeFunc` |

### ItemClickFunc: function

```ts
(cycleList: CycleList, index: number) => void
```

| 参数      | 说明                       | 类型     |
| --------- | -------------------------- | -------- |
| cycleList | cycleList 实例             | `object` |
| index     | 点击的 item 索引 从 0 开始 | `number` |

### ItemFocusChangeFunc: function

```ts
(cycleList: CycleList, index: number, isFocus: boolean) => void
```

| 参数      | 说明                       | 类型     |
| --------- | -------------------------- | -------- |
| cycleList | cycleList 实例             | `object` |
| index     | 失去/获取焦点 item 的索引 | `number` |
| isFocus     | 是否获取焦点 | `boolean` |

## 属性访问支持列表

| 属性名                 | setProperty | getProperty | [setter](../gettersetter.mdx) | [getter](../gettersetter.mdx) |
|------------------------|-------------|-------------|-------------------------------|-------------------------------|
| x                     | Y           | Y           | Y                             | Y                             |
| y                     | Y           | Y           | Y                             | Y                             |
| w                     | Y           | Y           | Y                             | Y                             |
| h                     | Y           | Y           | Y                             | Y                             |
| data_array            | N           | N           | N                             | Y                             |
| data_size             | N           | N           | N                             | Y                             |
| item_bg_color         | N           | N           | N                             | Y                             |
| item_height           | N           | N           | N                             | Y                             |
| item_click_func       | N           | N           | Y                             | Y                             |
| item_focus_change_func | N           | N           | Y                             | Y                             |

## 代码示例

:::tip
代码示例中的图片资源请参考 [设计资源](../../../../related-resources/design-resources.mdx)
:::

```js
import { createWidget, widget } from '@zos/ui'

Page({
  state: {
    pageName: 'CYCLE_LIST'
  },

  build() {
    const imgArray = ['number-img/0.png', 'number-img/1.png', 'number-img/2.png', 'number-img/3.png', 'number-img/4.png']
    const cycleList = createWidget(widget.CYCLE_LIST, {
      x: 230,
      y: 120,
      h: 300,
      w: 30,
      data_array: imgArray,
      data_size: 5,
      item_height: 100,
      item_click_func: (list, index) => {
        console.log(index)
      },
      item_bg_color: 0xffffff
    })
  }
})
```
