# scrollTo

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

滚动页面至指定位置。

## 类型

```ts
function scrollTo(option: Option): void
```

### 简化调用方式

```ts
function scrollTo(y: number): void
```

## 参数

### Option

| 属性       | 类型                    | 必填 | 默认值 | 说明                                                       | API_LEVEL |
| ---------- | ----------------------- | ---- | ------ | ---------------------------------------------------------- | --------- |
| y          | <code>number</code>     | 是   | -      | 页面的纵轴坐标，手表 12 点钟方向为正方向，即向下滚动为负值 | 2.0       |
| animConfig | <code>animConfig</code> | 否   | -      | 滚动动画配置                                               | 3.6       |

### animConfig

| 属性               | 类型                        | 必填 | 默认值          | 说明                                                                                                           | API_LEVEL |
| ------------------ | --------------------------- | ---- | --------------- | -------------------------------------------------------------------------------------------------------------- | --------- |
| anim_rate          | <code>string</code>         | 否   | -               | 动画曲线，可选值 `linear`, `easein`, `easeout`, `easeinout`，参考 [https://easings.net/](https://easings.net/) | 3.6       |
| anim_duration      | <code>number</code>         | 否   | -               | 动画时长，单位毫秒                                                                                             | 3.6       |
| anim_fps           | <code>number</code>         | 否   | <code>25</code> | 动画帧率                                                                                                       | 3.6       |
| anim_complete_func | <code>() =&#62; void</code> | 否   | -               | 动画播放结束回调函数                                                                                           | 3.6       |

## 代码示例

```js
import { scrollTo } from '@zos/page'

scrollTo({
  y: -200,
})
```
