---
sidebar_label: 设计价值观
---

# 设计价值观

设计价值观是设计系统的核⼼，它为 Zepp OS 的产品设计提供了准则和方向。

## 自然 \| Nature

自然的设计能通过唤起用户的认知把真实世界的体验与产品设计连接起来，使用户快速理解和产生共情，并保障穿戴设备的舒适体验；

**认知自然** 在智能穿戴界面所呈现的元素和内容，通过提炼自然元素，使用符合自然規律的视觉、听觉、触觉等認知手段来表达，从而做到不言而喻，降低用户认知成本，创造和谐的视觉享受和自然的产品体验。

**交互自然** 系统操作需要符合用户的直觉，与现实生活的流程、逻辑、习惯保持一致，通过场景定义、行为分析、云计算、人工算法等手段主动适应用户的习惯，置界面于用户控制之下，降低理解成本和使用门槛，让交互行为更自然。

## 简单 \| Simple

Zepp OS 是为可穿戴设备而设计的系统，因此产品设计应该结合设备特征，分析使用场景，专注核心功能，适宜穿戴者，为他们提供简单轻量的用户体验。

**界⾯简单** 元素样式简单直白，页面结构层次清晰，明确核心功能，提取焦点信息，提高用户的信息获取效率；

**操作简单** 智能穿戴设备的操作需要适应小屏幕操作，使用短路径设计操作流程，把复杂度转移到屏幕背后，让⽤户通过简单操作快速完成任务目标。

**理解简单** 在设计中充分考虑用户使用场景，通过渐进的指引及清晰的表意逐步展现高级功能，尽可能的减少⽤户的学习成本和感知成本，让用户快速理解进而作出决策；

## 共生 \| Symbiosis

作为一个运行在贴身穿戴设备上的系统，我们与用户紧密连接，相生相伴，设计者应在产品设计中充分考虑用户需求、产品价值和技术发展，让人三者有机融合共同发展。

**体验共生** 可穿戴设备通过物理设备和穿戴者有了更亲密的连接，硬件和软件带给用户的体验应该是相辅相成共同作用的，好的系统设计以两者共同发展来思考，减少硬件和软件的物理边界，让用户获得更好的体验，从而使产品更加通用，更具竞争力。

**价值共生** 用户的意见、痛点、偏好和需求是产品设计的核心，设计者应建立系统设计思维，洞悉产品功能的价值，为用户提供服务，在价值和需求间建立连接，让产品价值被发现，从而帮助用户更好的关注自己，提升他们的健康和生活质量。
