# closeSync

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

同步地关闭文件句柄。

## 类型

```ts
function closeSync(option: Option): Result
```

### 简化调用方式

```ts
function closeSync(fd: number): Result
```

## 参数

### Option

| 属性 | 类型                | 必填 | 默认值 | 说明                                                  | API_LEVEL |
| ---- | ------------------- | ---- | ------ | ----------------------------------------------------- | --------- |
| fd   | <code>number</code> | 是   | -      | 文件句柄，由 `openSync`、`openAssetsSync` 等 API 返回 | 2.0       |

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { openSync, closeSync, O_RDONLY } from '@zos/fs'

const fd = openSync({
  path: 'test.txt',
  flag: O_RDONLY,
})
const result = closeSync({
  fd,
})

if (result === 0) {
  console.log('file descriptor closed')
}
```
