import { px } from '@zos/utils'
import { getDeviceInfo } from '@zos/device'
import ui from '@zos/ui'

export const { width: DEVICE_WIDTH, height: DEVICE_HEIGHT } = getDeviceInfo()

export const BTN_STYLE = {
  text: '测试蓝牙功能',
  x: px(42),
  y: px(200),
  w: DEVICE_WIDTH - px(42) * 2,
  h: px(100),
  color: 0x0000ff,
  text_size: px(36),
  align_h: ui.align.CENTER_H,
  text_style: ui.text_style.WRAP,
}
