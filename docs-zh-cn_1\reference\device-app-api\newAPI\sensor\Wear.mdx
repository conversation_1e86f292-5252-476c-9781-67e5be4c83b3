---
title: Wear
sidebar_label: Wear 佩戴状态
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

佩戴状态传感器。

## 方法

### getStatus

获取当前设备佩戴状态，`0`：未佩戴、`1`：佩戴、`2`：运动中、`3`：不确定

```ts
getStatus(): number
```

### onChange

注册设备佩戴状态变化事件监听回调函数

```ts
onChange(callback: () => void): void
```

### offChange

取消设备佩戴状态变化事件监听回调函数

```ts
offChange(callback: () => void): void
```

## 代码示例

```js
import { Wear } from '@zos/sensor'

const wear = new Wear()
const status = wear.getStatus()
const callback = () => {
  console.log(wear.getStatus())
}

wear.onChange(callback)

// When not needed for use
wear.offChange(callback)
```
