# mstGetConnIdByRemoteAddr

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

根据从机 MAC 地址查询连接 Id。

## 类型

```ts
function mstGetConnIdByRemoteAddr(deviceAddress: <PERSON>ceAddress): Result
```

## 参数

### DeviceAddress

| 类型                     | 说明                                                 |
| ------------------------ | ---------------------------------------------------- |
| <code>ArrayBuffer</code> | 设备 MAC 地址，长度 6 字节，建议使用 Uint8Array 视图 |

### Result

| 类型                               | 说明                                                             |
| ---------------------------------- | ---------------------------------------------------------------- |
| <code>number&#124;undefined</code> | 函数调用结果，查询成功返回 `connectId`，查询失败返回 `undefined` |

## 代码示例

```js
import { mstGetConnIdByRemoteAddr } from '@zos/ble'

// ...
```
