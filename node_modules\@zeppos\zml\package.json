{"name": "@zeppos/zml", "version": "0.0.9", "description": "A Mini Library of ZeppOS MiniApp", "zeppos": true, "typings": "./zml.d.ts", "exports": {".": null, "./base-app": "./dist/zml-app.js", "./base-app.debug": "./dist/zml-app.debug.js", "./base-page": "./dist/zml-page.js", "./base-page.debug": "./dist/zml-page.debug.js", "./base-side": "./dist/zml-side.js", "./base-side.debug": "./dist/zml-side.debug.js"}, "files": ["dist", "zml.d.ts", "examples", "README_CN.md", "README.md"], "scripts": {"watch": "rollup -w -c rollup.config.mjs", "build": "NODE_ENV=production rollup -c rollup.config.mjs", "format": "prettier src -write"}, "author": "h<PERSON><PERSON>h", "license": "MIT", "devDependencies": {"@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-commonjs": "^25.0.4", "@rollup/plugin-node-resolve": "^15.2.1", "@rollup/plugin-replace": "^5.0.2", "@rollup/plugin-terser": "^0.4.3", "prettier": "^3.0.2", "rollup": "^3.28.1"}}