---
title: hmFS.SysProGetChars(key)
sidebar_label: SysProGetChars
---

获取临时存储的字符串，系统重启将清除

## 类型

```ts
(key: string) => result
```

## 参数

### key

| 说明     | 必填 | 类型     | 默认值 |
| -------- | ---- | -------- | ------ |
| 键字符串 | 是   | `string` | -      |

### result

| 说明         | 类型      |
| ------------ | --------- |
| 存储的字符串 | `string` |

## 用法

```js
hmFS.SysProSetChars('js_test_char', 'hello')
console.log(hmFS.SysProGetChars('js_test_char'))
```
