---
sidebar_label: 副屏
---

# 副屏  

副屏是用户能快速查看各个应用的部分核心功能的入口，所以一个应用可以定义多个副屏，但一个副屏只对应一个应用。

## 设计原则  

专注：副屏的设计应该保持专注于一个任务，而不是完整的应用体验、聚焦，降低用户的负担，保持友好的体验。  

掌控：设备始终接触用户，应结合场景、状态、上下文更新内容，例如时间、地点、活动、是否联网等。

## 使用规则  

- 点击实体键回到表盘。
- 不允许用户在页面内的上下滑动。
- 上下滑动不能呼出通知中心、控制中心。
- 不允许长页面设计。
- 点击副屏交互整体分为两类：  
  类型一：点击空白区域可进入应用首页，如：活动目标、PAI。  

  类型二：点击不需要进入应用首页就能完成：秒表、倒计时、支付宝。  

  具体每个副屏是否支持点击进入在每个副屏描述定义。

## 视觉规范  

- 字体字号  

需要突出的大数字可以使用特殊风格字体，正文与辅助性文字应选择相对应的字号的通用字体。  

正文、提示性文字、单位、最小字号应该不小于副标题字号  

图表类的说明文字使用统一的字号  

![Design](/img/design/customization_28.png)  

不同副屏所需要突出的数字，如果布局较为接近的时候字号和基线应在同一个水平，单位和数字之间的间距保持统一且与主体数字基线底部对齐。  

![Design](/img/design/customization_29.png)  

- 图表与进度  

副屏使用的进度条应保持粗细和边距的统一。  

![Design](/img/design/customization_30.png)  

在遇到圆形屏幕和方形屏幕之间因为按钮形态变化导致空间不够的情况，可以适当调整。  

![Design](/img/design/customization_31.png)

## 副屏页面的特殊状态  

仅通过图示及文字告知当前页面状态或可改变当前状态的解决方式，但不支持用户在当前页面执行操作。  

- 无操作的空页面。  

使用图标和文字的组合，整体在屏幕里居中显示，图标使用彩色图标。  

![Design](/img/design/customization_32.png)

- 有操作的空页面。  

底部加入按钮，整体对齐方式使用垂直居中的形式，保持上下间距统一。

![Design](/img/design/customization_33.png)

- 需要授权的页面。  

![Design](/img/design/customization_34.png)

- 无数据/数据加载中。  

数据为空的状态使用“--”来占位表示。  

![Design](/img/design/customization_35.png)

## 预览图

长按副屏进入编辑状态，需提供一张静态预览图，预览图尺寸与屏幕分辨率一致，如果是方屏幕设备，需要加入圆角。屏幕分辨率和圆角信息参考 [设备基本信息](../../reference/related-resources/device-list.mdx)。（目前仅 Zepp OS 3.0 及以上版本支持长按副屏进入编辑功能）

![Design](/img/design/widget_preview.png)

Figma 设计模版文件链接：[Widget templates](https://www.figma.com/community/file/1372844899307211702)

## 开发文档

[注册副屏应用和快捷卡片](../../guides/framework/device/secondary-widget.md)
