import { createWidget, widget, align, prop, text_style } from '@zos/ui'
import { showToast } from '@zos/interaction'
import { px } from '@zos/utils'

// 进制转换字符集
const CHARS = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

Page({
  state: {
    currentInput: '0',
    currentBase: 10,
    widgets: {}
  },

  onInit() {
    console.log('Base Converter Page Init')
  },

  build() {
    // 创建黑色背景
    this.state.widgets.background = createWidget(widget.FILL_RECT, {
      x: 0,
      y: 0,
      w: px(480),
      h: px(480),
      color: 0x000000
    })

    // 创建标题
    this.state.widgets.title = createWidget(widget.TEXT, {
      x: px(20),
      y: px(20),
      w: px(440),
      h: px(48),
      text: '进制转换器',
      text_size: px(18),
      color: 0xffffff,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })

    // 创建输入显示区域
    this.state.widgets.inputDisplay = createWidget(widget.TEXT, {
      x: px(20),
      y: px(80),
      w: px(440),
      h: px(40),
      text: '0',
      text_size: px(24),
      color: 0x4CAF50,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })

    // 创建当前进制显示
    this.state.widgets.currentBase = createWidget(widget.TEXT, {
      x: px(20),
      y: px(130),
      w: px(440),
      h: px(30),
      text: '当前: 十进制 (10)',
      text_size: px(14),
      color: 0x888888,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })

    // 创建结果显示区域
    this.createResults()
    
    // 创建简单的数字键盘
    this.createSimpleKeyboard()

    // 初始化转换
    this.convertAll()
  },

  createResults() {
    const bases = [2, 8, 10, 16, 36, 62]
    const baseNames = ['二进制', '八进制', '十进制', '十六进制', '三十六进制', '六十二进制']
    
    bases.forEach((base, index) => {
      const yPos = 180 + index * 35

      // 进制标签
      this.state.widgets[`label_${base}`] = createWidget(widget.TEXT, {
        x: px(30),
        y: px(yPos),
        w: px(120),
        h: px(30),
        text: `${baseNames[index]}:`,
        text_size: px(14),
        color: 0x888888,
        align_h: align.LEFT,
        align_v: align.CENTER_V
      })

      // 结果值
      this.state.widgets[`result_${base}`] = createWidget(widget.TEXT, {
        x: px(160),
        y: px(yPos),
        w: px(290),
        h: px(30),
        text: '0',
        text_size: px(16),
        color: 0xffffff,
        align_h: align.RIGHT,
        align_v: align.CENTER_V
      })
    })
  },

  createSimpleKeyboard() {
    // 数字键 0-9
    for (let i = 0; i <= 9; i++) {
      const col = i === 0 ? 9 : (i - 1) % 10
      const row = i === 0 ? 1 : Math.floor((i - 1) / 5)
      
      this.state.widgets[`key_${i}`] = createWidget(widget.BUTTON, {
        x: px(40 + col * 40),
        y: px(400 + row * 50),
        w: px(35),
        h: px(40),
        text: i.toString(),
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x333333,
        press_color: 0x555555,
        radius: px(8),
        click_func: () => {
          this.inputDigit(i.toString())
        }
      })
    }

    // 字母键 A-F (十六进制)
    const letters = ['A', 'B', 'C', 'D', 'E', 'F']
    letters.forEach((letter, index) => {
      this.state.widgets[`key_${letter}`] = createWidget(widget.BUTTON, {
        x: px(40 + index * 40),
        y: px(500),
        w: px(35),
        h: px(40),
        text: letter,
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x333333,
        press_color: 0x555555,
        radius: px(8),
        click_func: () => {
          this.inputDigit(letter)
        }
      })
    })

    // 删除键
    this.state.widgets.deleteKey = createWidget(widget.BUTTON, {
      x: px(280),
      y: px(500),
      w: px(60),
      h: px(40),
      text: '删除',
      text_size: px(14),
      color: 0xffffff,
      normal_color: 0xf44336,
      press_color: 0xff6659,
      radius: px(8),
      click_func: () => {
        this.deleteDigit()
      }
    })

    // 清空键
    this.state.widgets.clearKey = createWidget(widget.BUTTON, {
      x: px(350),
      y: px(500),
      w: px(60),
      h: px(40),
      text: '清空',
      text_size: px(14),
      color: 0xffffff,
      normal_color: 0xff9800,
      press_color: 0xffb74d,
      radius: px(8),
      click_func: () => {
        this.clearInput()
      }
    })
  },

  // 输入数字/字母
  inputDigit(digit) {
    if (this.state.currentInput === '0') {
      this.state.currentInput = digit
    } else {
      this.state.currentInput += digit
    }
    this.updateDisplay()
    this.convertAll()
  },

  // 删除最后一位
  deleteDigit() {
    if (this.state.currentInput.length > 1) {
      this.state.currentInput = this.state.currentInput.slice(0, -1)
    } else {
      this.state.currentInput = '0'
    }
    this.updateDisplay()
    this.convertAll()
  },

  // 清空输入
  clearInput() {
    this.state.currentInput = '0'
    this.updateDisplay()
    this.convertAll()
  },

  // 更新显示
  updateDisplay() {
    this.state.widgets.inputDisplay.setProperty(prop.TEXT, this.state.currentInput)
  },

  // 转换为十进制
  toDecimal(value, fromBase) {
    let result = 0
    for (let i = 0; i < value.length; i++) {
      const digit = CHARS.indexOf(value[i].toLowerCase())
      if (digit >= fromBase) return NaN
      result = result * fromBase + digit
    }
    return result
  },

  // 从十进制转换
  fromDecimal(decimal, toBase) {
    if (decimal === 0) return '0'
    let result = ''
    while (decimal > 0) {
      result = CHARS[decimal % toBase] + result
      decimal = Math.floor(decimal / toBase)
    }
    return result
  },

  // 转换所有进制
  convertAll() {
    const decimal = this.toDecimal(this.state.currentInput, this.state.currentBase)
    
    if (isNaN(decimal)) {
      showToast({
        content: '输入无效'
      })
      return
    }

    const bases = [2, 8, 10, 16, 36, 62]
    bases.forEach(base => {
      const result = this.fromDecimal(decimal, base)
      const displayResult = result.toUpperCase()
      this.state.widgets[`result_${base}`].setProperty(prop.TEXT, displayResult)
    })
  }
})
