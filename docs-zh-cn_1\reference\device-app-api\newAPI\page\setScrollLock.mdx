# setScrollLock

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

设置当前页面滚动位置锁定，即屏幕位置不会跟随手势滑动改变。调用此 API 执行解锁操作之后，页面滚动模式会设置为自由滚动模式。

## 类型

```ts
function setScrollLock(option: Option): void
```

## 参数

### Option

| 属性 | 类型                 | 必填 | 默认值            | 说明                     | API_LEVEL |
| ---- | -------------------- | ---- | ----------------- | ------------------------ | --------- |
| lock | <code>boolean</code> | 否   | <code>true</code> | 是否锁定当前页面滚动位置 | 2.0       |

## 代码示例

```js
import { setScrollLock } from '@zos/page'

setScrollLock({
  lock: true,
})
```
