---
sidebar_label: 界面排版
---

# 界面排版

## 图文分离

不要将文字直接嵌入图片，图片上的文字建议采用图文分层制作。  

![Design](/img/design/interface-layouts_1.png)

## 兼容多语言文本  

不同语言的文本翻译后的长度存在较大差异，因此在文案撰写和翻译上，请尽量采用简洁、准确的描述以减少字符数量。同时，在设计界面及控件时，需要兼容不同长度的文本内容，为文本展示预留足够的空间，并设置不同场景下文本内容溢出的处理方法。

**预留充足的显示空间**  

控件要根据不同文本内容长度预留出足够的空间。例如，基于英文进行设计一个列表条目时，当字数小于10的时候，为其他语言预留50%的空间；当字数大于10小于20的时候，预留30%的空间，字数大于10小于20的时候，预留20%的空间。  

![Design](/img/design/interface-layouts_2.png)

**当文本内容超出容器宽度时，使用以下处理方式：**

1. 超出的内容使用省略号...表示
2. 滚动展示
3. 有限制的换行
4. 无限制的换行


**超出的内容使用省略号表示：**  

当文本内容超出容器宽度，且所在场景不适合换行或滚动显示时，可以采用省略号...来表示未展示完全的内容。  

![Design](/img/design/interface-layouts_3.png)  

>① 使用省略号...来表示未展示完全的内容
>
>② 示例：系统状态栏标题 Watch face & Time，显示为 Watch face & ... 

**滚动展示：**  

当文本内容超出容器宽度，且容器高度不支持换行展示，而文本内容必须完整露出时，可以使用“滚动”效果。  

![Design](/img/design/interface-layouts_4.png)  

>① 文本滚动显示效果
>
>② 示例：运动开始前界面，滚动显示的运动名称。

详细的滚动效果参数，请参考：[视觉-动效-文本滚动效果](../visual/animations.md#文本滚动展示)

**有限制的换行：**  

不同场景下的文本容器，对可容纳的最多行数有不同的限制。  

![Design](/img/design/interface-layouts_5.png)  

>① 示例：应用列表页面，应用名称最多显示2行。
>
>② 示例：设置页面，设置项名称最多显示3行。

**无限制的换行：**  

当容器对文本的长度没有特殊限制时，应通过换行来完整展示全部文本内容。需要注意的是，虽然此场景下不限制文本内容的行数，但请尽量使用简洁、准确的文案以减少字符数量，以保证内容可读性。  

![Design](/img/design/interface-layouts_6.png)  

>① 示例：错误提示页面的提示文本
>
>② 示例：通知页面的详细内容文本


**超长单词换行规则：**  

一般情况下，文本内容的换行应在单词之间进行，避免将单个单词拆分进行换行。  

![Design](/img/design/interface-layouts_7.png)  

>① 示例：应用名称 Fréquence Cardiaque
>
>② 在单词之间换行
>
>③ 避免将单个单词拆分

**当单个单词的长度超出容器宽度时，可以对单词进行强制换行，并在换行处添加连字符 -。**  

![Design](/img/design/interface-layouts_8.png)  

>① 示例：单词 Microminiaturization  
>
>② 单个单词超出了容器宽度，对单词进行强制换行，并在换行位置添加连字符 -


## 正确处理 RTL 语言  

RTL（Right to Left，从右到左）语言指的是从右侧向左侧书写的语言。此语言的普遍特征有：句子从右到左阅读；事件发展顺序从右到左进行；左箭头 ← 表示向前运动，右箭头 → 代表向后运动  

![Design](/img/design/interface-layouts_9.png)  

>①当 UI 从一个方向更改为另一个方向时，未翻译的文本（即使它是短语的一部分）和数字不会被镜像，应保持与其所用语言相符的正确方向。
>
>②传达方向的操作，应镜像。
>
>③不传达方向的图标，应保持一致，只改变位置。
>
>④传达方向的图标，应镜像。
>
>⑤固定词语被翻译后从右侧向左侧书写。

## RTL 设计用例

### 滑块 - 音量条

基于阅读方向差异绘制音量图标  

![Design](/img/design/interface-layouts_10.png)  

>① 常规语言图标样式   ② RTL 语言环境图标样式

### 开关  

镜像显示开关开启、关闭方向  

![Design](/img/design/interface-layouts_11.png)  

>①开启：开关开启      ②关闭：开关关闭      ③开启（禁用）      ④关闭（禁用）  

### 按钮 - 悬浮按钮  

镜像显示并排图标位置  

![Design](/img/design/interface-layouts_12.png)  

>①常规语言图标显示  ②RTL 语言环境图标显示

### 滚动条

滚动条显示在页面左侧  

![Design](/img/design/interface-layouts_13.png)  

### 回复选择  

表情回复界面中，镜像输入方向、字符光标、删除按钮的位置  

![Design](/img/design/interface-layouts_14.png)  

>①点击表情  ②输入过程  ③超出屏幕   

### 顶部状态栏（方屏设备 ）  

镜像显示时间与页面标题文本位置  

![Design](/img/design/interface-layouts_15.png)  

>①常规语言显示  ②RTL 语言环境显示