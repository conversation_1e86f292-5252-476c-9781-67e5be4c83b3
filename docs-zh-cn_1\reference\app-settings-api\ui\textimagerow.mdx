---
title: TextImageRow 图标和标题
sidebar_label: TextImageRow 图标和标题
---

## 类型

```ts
(props: Props) => result: RenderFunc
```

## Props: object

| 名称      | 说明                          | 必填 | 类型      | 默认值 |
| --------- | ----------------------------- | ---- | --------- | ------ |
| label     | TextImageRow 的主要文本       | 否   | `string`  | -      |
| sublabel  | 标签下面的灰色文本            | 否   | `string`  | -      |
| icon      | 图标的来源                    | 否   | `string`  | -      |
| iconColor | 图标的颜色                    | 否   | `string`  | -      |
| rounded   | 如果为 true，则图标是圆形的。 | 否   | `boolean` | -      |
| iconRight | 如果为 true，则图标位于右侧。 | 否   | `boolean` | -      |
