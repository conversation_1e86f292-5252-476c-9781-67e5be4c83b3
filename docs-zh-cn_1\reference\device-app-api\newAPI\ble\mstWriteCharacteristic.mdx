# mstWriteCharacteristic

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

写 Characteristic 信息。

## 类型

```ts
function mstWriteCharacteristic(profile: Profile, uuid: UUID, data: Data, length: Length): void
```

## 参数

### Profile

| 类型                | 说明         |
| ------------------- | ------------ |
| <code>number</code> | Profile 指针 |

### UUID

| 类型                | 说明                       |
| ------------------- | -------------------------- |
| <code>string</code> | Characteristic UUID 字符串 |

### Data

| 类型                     | 说明                                   |
| ------------------------ | -------------------------------------- |
| <code>ArrayBuffer</code> | 读取到的数据，使用 Uint8Array 视图读取 |

### Length

| 类型                | 说明     |
| ------------------- | -------- |
| <code>number</code> | 数据长度 |

## 代码示例

```js
import { mstWriteCharacteristic } from '@zos/ble'

// ...
```
