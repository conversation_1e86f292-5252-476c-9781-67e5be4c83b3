# mstOnServiceChangeEnd

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册 Service 变更结束回调函数。

## 类型

```ts
function mstOnServiceChangeEnd(callback: Callback): Result
```

## 参数

### Callback

| 类型                                        | 说明                     |
| ------------------------------------------- | ------------------------ |
| <code>(profile: Profile) =&#62; void</code> | Service 变更结束回调函数 |

### Profile

| 类型                | 说明         |
| ------------------- | ------------ |
| <code>number</code> | Profile 指针 |

### Result

| 类型                 | 说明                                            |
| -------------------- | ----------------------------------------------- |
| <code>boolean</code> | 函数调用结果，`true` 表示成功、`false` 表示失败 |

## 代码示例

```js
import { mstOnServiceChangeEnd } from '@zos/ble'

// ...
```
