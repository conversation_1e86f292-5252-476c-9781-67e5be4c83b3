---
title: Gyroscope
sidebar_label: Gyroscope 陀螺仪
---

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

![Gyroscope_image}](https://img-cdn.zepp.com/20231219/4ede3747e830cb5adf0cfaa79ba59e0a.jpeg)

陀螺仪。测量设备沿三个正交轴（x,y,z）旋转的角速度，x、y 轴与屏幕平行，正方向参考图示，z 轴垂直于设备的屏幕，正方向指向上方，旋转角速度的方向使用[右手定则](https://en.wikipedia.org/wiki/Right-hand_rule)来确定，图示旋转箭头方向为正方向。

:::info
权限代码： `device:os.gyroscope`
:::

## 方法

### start

开始监听陀螺仪数据

```ts
start(): void
```

### stop

停止监听陀螺仪数据

```ts
stop(): void
```

### getCurrent

获取当前陀螺仪数据

```ts
getCurrent(): Result
```

#### Result

| 属性 | 类型                | 说明                            | API_LEVEL |
| ---- | ------------------- | ------------------------------- | --------- |
| x    | <code>number</code> | x 轴的角速度，单位 DPS，度数/秒 | 3.0       |
| y    | <code>number</code> | y 轴的角速度，单位 DPS，度数/秒 | 3.0       |
| z    | <code>number</code> | z 轴的角速度，单位 DPS，度数/秒 | 3.0       |

### onChange

注册陀螺仪数据变化事件监听回调函数

```ts
onChange(callback: () => void): void
```

### offChange

取消陀螺仪数据变化事件监听回调函数

```ts
offChange(callback: () => void): void
```

### setFreqMode

> API_LEVEL `3.0`

设置触发频率的模式，`mode` 值参考频率模式常量

```ts
setFreqMode(mode: number): void
```

#### 常量

##### 频率模式

| 常量               | 说明                       | API_LEVEL |
| ------------------ | -------------------------- | --------- |
| `FREQ_MODE_LOW`    | 低功耗模式，触发频率低     | 3.0       |
| `FREQ_MODE_NORMAL` | 正常功耗模式，触发频率中等 | 3.0       |
| `FREQ_MODE_HIGH`   | 高功耗模式，触发频率高     | 3.0       |

### getFreqMode

> API_LEVEL `3.0`

获取触发频率模式，结果值参考频率模式常量

```ts
getFreqMode(): number
```

#### 常量

##### 频率模式

| 常量               | 说明                       | API_LEVEL |
| ------------------ | -------------------------- | --------- |
| `FREQ_MODE_LOW`    | 低功耗模式，触发频率低     | 3.0       |
| `FREQ_MODE_NORMAL` | 正常功耗模式，触发频率中等 | 3.0       |
| `FREQ_MODE_HIGH`   | 高功耗模式，触发频率高     | 3.0       |

## 代码示例

```js
import { Gyroscope, FREQ_MODE_LOW } from '@zos/sensor'

const gyroscope = new Gyroscope()

const callback = () => {
  console.log(gyroscope.getCurrent())
}
gyroscope.onChange(callback)
gyroscope.setFreqMode(FREQ_MODE_LOW)
gyroscope.start()

// When not needed for use
gyroscope.offChange()
gyroscope.stop()
```
