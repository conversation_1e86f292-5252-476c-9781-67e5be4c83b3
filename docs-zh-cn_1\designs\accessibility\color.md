---
sidebar_label: 色彩
---


# 色彩  

## 避免使用颜色作为传达重要信息的唯一方式  

许多视觉障碍用户很难区分蓝色和橙色，或者红色和绿色，在应用设计过程中需要避免只使用颜色来传达信息，也需要结合文字或图形，来帮助视觉障碍用户理解。  

开关提供了一种标签来描述切换控件的状态，借助图形来告知用户对应选择的开/关状态。  

![Design](/img/design/color.png)
>①常规开关样式   ②带标签的开关样式

## 无障碍色板应该满足视觉障碍用户的识别需求  

可以通过模拟类似色彩缺陷效果，找到安全的色板。可使用色彩缺陷模拟器辅助进行设计：[模拟器](https://www.colorhexa.com/d14221)  

![Design](/img/design/3bf292eb0e6389150a27e7f80e9f6d9c.png)  

- 两个需要区分的颜色，需要足够的色相差异，保证△Euv≥20(ISO9421-307)。  
    色差在CIE L*A*B*色彩空间中的色差值△Eab*，在CIE LUV色彩空间中色差值△Euv*   

    [颜色工具集](https://www.colortell.com/colortool)  ：用来转化RGB色彩到L*A*B*色彩空间与CIE LUV色彩空间。  
      
    [色差计算器](https://m.qtccolor.com/tool/colorde.aspx)  ：用来计算△Euv。  

![Design](/img/design/d558d054b485affa60a96baca7c1b7df.png)