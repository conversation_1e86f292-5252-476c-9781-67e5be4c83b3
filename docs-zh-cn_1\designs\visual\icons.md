---
sidebar_label: 图标
---
# 图标

图标是为用户提供信息指引的特定图形。Zepp OS 提供多种类型的图标元素，以适合不同场景下的应用。本规范列出了使用场景、图标构成、图形造型、图标尺寸、资源输出等规则，以指导图标元素的使用。

## 设计原则

Zepp OS 的图标遵循「轻量」与「友好」的设计理念。在造型上使用简洁的几何构形，精简冗余细节，保证识别度，清晰明确地传达其含义；在细节上避免锐利边角，通过连续曲率的线条传达自然美感。

## 图标类型

- 应用程序图标
- 通用功能图标
- 按钮图标

## 应用程序图标

应用程序图标应清晰明确地传达其功能/品牌/服务信息，设计风格应跟随 Zepp OS 的系统应用图标，并按照规范要求提供多个尺寸资源，以适合设备端、手机端应用程序商店等不同使用场景。  

- 应用程序图标整体为正圆形，尺寸为 248×248px，内部需预留空白透明的安全区域（上下左右各 4px）。  

![Design](/img/design/app-icons_1.png)  

>① 应用图标内容尺寸 
>
>② 透明安全区域 
>
>③ 应用图标最终尺寸

- 图标的主体内容大小不要超出圆形背板区域。  

![Design](/img/design/app-icons_2.png)  

>① 主体内容大小不要超出背板区域

- 图标应避免使用纯黑色背板，避免包含透明区域或透明度。  

![Design](/img/design/app-icons_3.png)  

>① 不要使用纯黑色背板
>
>② 背板区域不要包含透明区域
>
>③ 不要包含透明度

- 不要使用非圆形的背板。  

![Design](/img/design/app-icons_4.png)  

>① 不要使用非圆形的背板

- 图标资源应输出包含空白透明安全区域的PNG格式图片。  

![Design](/img/design/app-icons_5.png)  

>① 背板四周应添加空白透明的安全区域
>
>② 背板应该为圆形，避免使用其他形状
>
>③ 非图标区域应保持空白透明

- 设计示例  

![Design](/img/design/app-icons_6.png)

>① 图标内容尺寸240×240px
>
>② 四周各4px空白透明的安全区域
>
>③ 应用图标最终尺寸248×248px

- 实际使用效果示例  

![Design](/img/design/app-icons_7.png)  

>① 多变视图中的「网易云音乐」应用程序图标
>
>② 经典视图中的「网易云音乐」应用程序图标


**图标资源输出**  

- 尺寸对照表：  

<table>
    <tr>
        <th>设备分辨率</th>
        <th>480×480px</th>
    </tr>
    <tr>
        <td>应用程序图标</td>
        <td>248×248px（图标内部需包含空白透明安全区域，上下左右各 4px）</td>
    </tr>
    <tr>
        <td>应用商店展示用图标</td>
        <td>240×240px</td>
    </tr>
</table>

- 格式：PNG 格式，四周空白区域透明。


## 通用功能图标  

功能图标主要用于系统或功能的信息传达及状态指示。通用功能图标包括常规尺寸、中等尺寸、超小尺寸。

**使用示例:**  

![Design](/img/design/general-feature-icons_1.png)

![Design](/img/design/general-feature-icons_2.png)  

>①设置页面：列表图标（常规尺寸）
>
>②温度页面：温度计图标（常规尺寸）

![Design](/img/design/53131af143a11a917a844fa935337c51.png)

![Design](/img/design/392f3fd895dd550a899ec76fb763a0d9.png)  

>① 太阳与月亮页面：月升/日落时间图标（中等尺寸）
>
>② 运动前页面：心率图标（中等尺寸）

![Design](/img/design/general-feature-icons_3.png)

>① 通话记录卡片：去电图标（超小尺寸）
>
>② 事项提醒卡片：位置图标（超小尺寸）

**图标资源输出：**  

- 尺寸对照表：  

<table>
    <tr>
        <th>设备分辨率</th>
        <th>480×480px</th>
    </tr>
    <tr>
        <td>常规尺寸</td>
        <td>64×64px</td>
    </tr>
    <tr>
        <td>中等尺寸</td>
        <td>52×52px</td>
    </tr>
    <tr>
        <td>超小尺寸</td>
        <td>32×32px</td>
    </tr>
    <tr>
        <td colspan="5">注意：图标需包含空白透明的安全区域（上下左右各 2px）。</td>
    </tr>
</table>

- 格式：PNG 格式，四周空白区域透明。

## 按钮图标

按钮图标用于表示特定操作按钮的动作、导向。按钮图标包括常规尺寸、中等尺寸、小尺寸和超小尺寸。

**使用示例:**

![Design](/img/design/button-icons_1.png)

>① 刷新按钮图标（常规尺寸）
>
>② 确认按钮图标（常规尺寸）
>
>③ 退格按钮图标（常规尺寸）

![Design](/img/design/button-icons_2.png)

>① 列表右侧添加按钮图标（中等尺寸）
>
>② 下载按钮（中等尺寸）

![Design](/img/design/button-icons_3.png)

>① 删除按钮图标（常规尺寸）
>
>② 挂断按钮图标（常规尺寸）

![Design](/img/design/button-icons_4.png)

>① 刷新按钮图标（中等尺寸）
>
>② 确认按钮图标（中等尺寸）
>
>③ 退格按钮图标（中等尺寸）

![Design](/img/design/button-icons_5.png)

![Design](/img/design/button-icons_6.png)

>① 表盘编辑按钮图标（小尺寸）
>
>② 页面底部解释说明按钮图标（小尺寸）

![Design](/img/design/button-icons_7.png)

![Design](/img/design/button-icons_8.png)

>① 文字信息后的解释说明按钮图标（超小尺寸）

**图标资源输出：**

- 尺寸对照表：

<table>
    <tr>
        <th>设备分辨率</th>
        <th>480×480px</th>
    </tr>
    <tr>
        <td>常规尺寸</td>
        <td>64×64px</td>
    </tr>
    <tr>
        <td>中等尺寸</td>
        <td>52×52px</td>
    </tr>
     <tr>
        <td>小尺寸</td>
        <td>40×40px</td>
    </tr>
    <tr>
        <td>超小尺寸</td>
        <td>32×32px</td>
    </tr>
    <tr>
        <td colspan="5">注意：图标尺寸需包含空白透明的安全区域（上下左右各 2px）。</td>
    </tr>
</table>

- 格式：PNG 格式，四周空白区域透明。
