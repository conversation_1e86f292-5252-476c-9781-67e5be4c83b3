# resetDropWristScreenOff

> API_LEVEL `2.1` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

恢复落腕息屏行为。

## 类型

```ts
function resetDropWristScreenOff(): Result
```

## 参数

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { pauseDropWristScreenOff, resetDropWristScreenOff } from '@zos/display'

pauseDropWristScreenOff({
  duration: 0,
})

setTimeout(() => {
  resetDropWristScreenOff()
}, 3000)
```
