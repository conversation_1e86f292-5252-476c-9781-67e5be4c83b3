# rmSync

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

同步地删除小程序 `/data` 目录下的文件。

## 类型

```ts
function rmSync(option: Option): Result
```

### 简化调用方式

```ts
function rmSync(path: string): Result
```

## 参数

### Option

| 属性 | 类型                | 必填 | 默认值 | 说明     | API_LEVEL |
| ---- | ------------------- | ---- | ------ | -------- | --------- |
| path | <code>string</code> | 是   | -      | 文件路径 | 2.0       |

### Result

| 类型                | 说明                    |
| ------------------- | ----------------------- |
| <code>number</code> | 如果返回 `0` 则表明成功 |

## 代码示例

```js
import { rmSync } from '@zos/fs'

const result = rmSync({
  path: 'test.txt',
})

if (result === 0) {
  console.log('rmSync success')
}
```
