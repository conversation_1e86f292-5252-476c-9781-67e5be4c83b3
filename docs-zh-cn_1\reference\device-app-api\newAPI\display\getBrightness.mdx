# getBrightness

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

获取当前设备的屏幕亮度。

## 类型

```ts
function getBrightness(): Result
```

## 参数

### Result

| 类型                | 说明                       |
| ------------------- | -------------------------- |
| <code>number</code> | 屏幕亮度数值，范围 0 - 100 |

## 代码示例

```js
import { getBrightness } from '@zos/display'

const result = getBrightness()
console.log(`current brightness ${result}`)
```
