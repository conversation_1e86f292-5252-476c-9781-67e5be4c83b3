# readdirSync

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

同步地读取小程序 `/data` 目录下的目录。

## 类型

```ts
function readdirSync(option: Option): Result
```

## 参数

### Option

| 属性 | 类型                | 必填 | 默认值 | 说明     | API_LEVEL |
| ---- | ------------------- | ---- | ------ | -------- | --------- |
| path | <code>string</code> | 是   | -      | 目录路径 | 2.0       |

### Result

| 类型                                              | 说明                                                  |
| ------------------------------------------------- | ----------------------------------------------------- |
| <code>Array&#60;string&#62;&#124;undefined</code> | 如果返回 `undefined` 则目录不存在，否则返回文件名数组 |

## 代码示例

```js
import { readdirSync } from '@zos/fs'

const result = readdirSync({
  path: 'content',
})
```
