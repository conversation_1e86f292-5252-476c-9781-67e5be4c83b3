import { createWidget, widget, align, prop, text_style } from '@zos/ui'
import { showToast } from '@zos/interaction'
import { px } from '@zos/utils'

// 进制转换字符集
const CHARS = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

Page({
  state: {
    currentInput: '0',
    currentBase: 10,
    currentBaseName: '十进制',
    capsLock: false,
    keyboardExpanded: false,
    widgets: {}
  },

  onInit() {
    console.log('Base Converter Page Init')
  },

  build() {
    // 创建渐变背景效果（用深色模拟）
    this.state.widgets.background = createWidget(widget.FILL_RECT, {
      x: 0,
      y: 0,
      w: px(480),
      h: px(480),
      color: 0x1a1a1a
    })

    // 创建标题栏背景
    this.state.widgets.titleBg = createWidget(widget.FILL_RECT, {
      x: px(20),
      y: px(20),
      w: px(440),
      h: px(48),
      radius: px(24),
      color: 0x0d0d0d // 半透明效果的近似
    })

    // 创建标题
    this.state.widgets.title = createWidget(widget.TEXT, {
      x: px(20),
      y: px(20),
      w: px(440),
      h: px(48),
      text: '进制转换器',
      text_size: px(18),
      color: 0xffffff,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })

    // 创建输入区域背景
    this.state.widgets.inputBg = createWidget(widget.FILL_RECT, {
      x: px(20),
      y: px(84),
      w: px(440),
      h: px(96),
      radius: px(16),
      color: 0x141414 // 半透明效果的近似
    })

    // 创建输入显示区域
    this.state.widgets.inputDisplay = createWidget(widget.TEXT, {
      x: px(32),
      y: px(96),
      w: px(416),
      h: px(32),
      text: '0',
      text_size: px(24),
      color: 0x4CAF50,
      align_h: align.LEFT,
      align_v: align.CENTER_V,
      text_style: text_style.WRAP
    })

    // 创建当前进制显示
    this.state.widgets.currentBase = createWidget(widget.TEXT, {
      x: px(32),
      y: px(140),
      w: px(416),
      h: px(24),
      text: '当前: 十进制 (10)',
      text_size: px(14),
      color: 0x888888,
      align_h: align.LEFT,
      align_v: align.CENTER_V
    })

    // 当前进制点击区域（用于打开进制选择器）
    this.state.widgets.baseClickArea = createWidget(widget.BUTTON, {
      x: px(20),
      y: px(84),
      w: px(440),
      h: px(96),
      normal_color: 0x00000000, // 透明
      press_color: 0x11111111, // 半透明
      radius: px(16),
      click_func: () => {
        this.showBaseSelector()
      }
    })

    // 创建结果显示区域
    this.createResults()

    // 创建键盘
    this.createSimpleKeyboard()

    // 创建进制选择器
    this.createBaseSelector()

    // 创建模式指示器
    this.createModeIndicator()

    // 初始化显示和转换
    this.updateDisplay()
    this.convertAll()
  },

  createResults() {
    // 结果区域背景
    this.state.widgets.resultsBg = createWidget(widget.FILL_RECT, {
      x: px(20),
      y: px(200),
      w: px(440),
      h: px(200),
      radius: px(16),
      color: 0x0d0d0d // 半透明效果的近似
    })

    const bases = [2, 8, 10, 16, 36, 62]
    const baseNames = ['二进制', '八进制', '十进制', '十六进制', '三十六进制', '六十二进制']

    bases.forEach((base, index) => {
      const yPos = 220 + index * 30

      // 进制标签
      this.state.widgets[`label_${base}`] = createWidget(widget.TEXT, {
        x: px(32),
        y: px(yPos),
        w: px(80),
        h: px(24),
        text: `${baseNames[index]}:`,
        text_size: px(14),
        color: 0x888888,
        align_h: align.LEFT,
        align_v: align.CENTER_V
      })

      // 结果值
      this.state.widgets[`result_${base}`] = createWidget(widget.TEXT, {
        x: px(124),
        y: px(yPos),
        w: px(324),
        h: px(24),
        text: '0',
        text_size: px(16),
        color: 0xffffff,
        align_h: align.RIGHT,
        align_v: align.CENTER_V,
        text_style: text_style.WRAP
      })

      // 添加分隔线（除了最后一个）
      if (index < bases.length - 1) {
        this.state.widgets[`separator_${base}`] = createWidget(widget.FILL_RECT, {
          x: px(32),
          y: px(yPos + 24),
          w: px(416),
          h: px(1),
          color: 0x1a1a1a // 分隔线颜色
        })
      }
    })
  },

  createSimpleKeyboard() {
    // 创建键盘容器背景（可收纳）
    this.state.widgets.keyboardContainer = createWidget(widget.FILL_RECT, {
      x: px(0),
      y: px(420), // 初始位置：大部分隐藏
      w: px(480),
      h: px(320),
      radius: px(20),
      color: 0x000000
    })

    // 键盘拖拽手柄
    this.state.widgets.keyboardHandle = createWidget(widget.FILL_RECT, {
      x: px(220),
      y: px(438),
      w: px(40),
      h: px(4),
      radius: px(2),
      color: 0x4d4d4d
    })

    // 键盘拖拽区域（点击展开/收起）
    this.state.widgets.keyboardHandleArea = createWidget(widget.BUTTON, {
      x: px(0),
      y: px(420),
      w: px(480),
      h: px(60),
      normal_color: 0x000000,
      press_color: 0x111111,
      radius: px(20),
      click_func: () => {
        this.toggleKeyboard()
      }
    })

    // 键盘提示文字
    this.state.widgets.keyboardHint = createWidget(widget.TEXT, {
      x: px(0),
      y: px(390),
      w: px(480),
      h: px(20),
      text: '上拉展开键盘',
      text_size: px(12),
      color: 0x4CAF50,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })

    this.createFullKeyboard()
  },

  createFullKeyboard() {
    // 数字键盘行 (1234567890⌫)
    const numbers = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0']
    numbers.forEach((num, index) => {
      this.state.widgets[`key_${num}`] = createWidget(widget.BUTTON, {
        x: px(20 + index * 40),
        y: px(480),
        w: px(36),
        h: px(40),
        text: num,
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x1a1a1a,
        press_color: 0x333333,
        radius: px(8),
        click_func: () => {
          this.inputDigit(num)
        }
      })
    })

    // 删除键
    this.state.widgets.deleteKey = createWidget(widget.BUTTON, {
      x: px(420),
      y: px(480),
      w: px(40),
      h: px(40),
      text: '⌫',
      text_size: px(18),
      color: 0xffffff,
      normal_color: 0xf44336,
      press_color: 0xff6659,
      radius: px(8),
      click_func: () => {
        this.deleteDigit()
      }
    })

    // 第一行字母 QWERTYUIOP
    const row1 = ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P']
    row1.forEach((letter, index) => {
      this.state.widgets[`key_${letter}`] = createWidget(widget.BUTTON, {
        x: px(20 + index * 44),
        y: px(530),
        w: px(40),
        h: px(40),
        text: this.state.capsLock ? letter : letter.toLowerCase(),
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x1a1a1a,
        press_color: 0x333333,
        radius: px(8),
        click_func: () => {
          this.inputDigit(this.state.capsLock ? letter : letter.toLowerCase())
        }
      })
    })

    // 第二行字母 ASDFGHJKL
    const row2 = ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L']
    row2.forEach((letter, index) => {
      this.state.widgets[`key_${letter}`] = createWidget(widget.BUTTON, {
        x: px(42 + index * 44),
        y: px(580),
        w: px(40),
        h: px(40),
        text: this.state.capsLock ? letter : letter.toLowerCase(),
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x1a1a1a,
        press_color: 0x333333,
        radius: px(8),
        click_func: () => {
          this.inputDigit(this.state.capsLock ? letter : letter.toLowerCase())
        }
      })
    })

    // 第三行：大小写切换 + ZXCVBNM
    this.state.widgets.capsKey = createWidget(widget.BUTTON, {
      x: px(20),
      y: px(630),
      w: px(50),
      h: px(40),
      text: '↑',
      text_size: px(14),
      color: this.state.capsLock ? 0x000000 : 0xffffff,
      normal_color: this.state.capsLock ? 0xFFC107 : 0x996600,
      press_color: this.state.capsLock ? 0xFFD54F : 0xFFB300,
      radius: px(8),
      click_func: () => {
        this.toggleCapsLock()
      }
    })

    const row3 = ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
    row3.forEach((letter, index) => {
      this.state.widgets[`key_${letter}`] = createWidget(widget.BUTTON, {
        x: px(80 + index * 44),
        y: px(630),
        w: px(40),
        h: px(40),
        text: this.state.capsLock ? letter : letter.toLowerCase(),
        text_size: px(16),
        color: 0xffffff,
        normal_color: 0x1a1a1a,
        press_color: 0x333333,
        radius: px(8),
        click_func: () => {
          this.inputDigit(this.state.capsLock ? letter : letter.toLowerCase())
        }
      })
    })

    // 功能键行
    this.state.widgets.baseSelectorKey = createWidget(widget.BUTTON, {
      x: px(20),
      y: px(680),
      w: px(200),
      h: px(40),
      text: '进制选择',
      text_size: px(14),
      color: 0xffffff,
      normal_color: 0x4CAF50,
      press_color: 0x66BB6A,
      radius: px(8),
      click_func: () => {
        this.showBaseSelector()
      }
    })

    this.state.widgets.clearKey = createWidget(widget.BUTTON, {
      x: px(240),
      y: px(680),
      w: px(100),
      h: px(40),
      text: '清空',
      text_size: px(14),
      color: 0xffffff,
      normal_color: 0xf44336,
      press_color: 0xff6659,
      radius: px(8),
      click_func: () => {
        this.clearInput()
      }
    })
  },

  // 创建进制选择器
  createBaseSelector() {
    // 选择器背景（初始隐藏）
    this.state.widgets.selectorBg = createWidget(widget.FILL_RECT, {
      x: px(90),
      y: px(140),
      w: px(300),
      h: px(200),
      radius: px(16),
      color: 0x000000
    })
    this.state.widgets.selectorBg.setProperty(prop.VISIBLE, false)

    // 选择器标题
    this.state.widgets.selectorTitle = createWidget(widget.TEXT, {
      x: px(90),
      y: px(150),
      w: px(300),
      h: px(30),
      text: '选择输入进制',
      text_size: px(16),
      color: 0xffffff,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })
    this.state.widgets.selectorTitle.setProperty(prop.VISIBLE, false)

    // 创建进制选项
    this.state.widgets.baseOptions = {}
    const bases = [2, 8, 10, 16, 36, 62]
    const baseNames = ['二进制', '八进制', '十进制', '十六进制', '三十六进制', '六十二进制']

    bases.forEach((base, index) => {
      const yPos = 190 + index * 25
      this.state.widgets.baseOptions[base] = createWidget(widget.BUTTON, {
        x: px(100),
        y: px(yPos),
        w: px(280),
        h: px(20),
        text: `${base === this.state.currentBase ? '●' : '○'} ${baseNames[index]} (${base})`,
        text_size: px(14),
        color: base === this.state.currentBase ? 0x4CAF50 : 0xffffff,
        normal_color: base === this.state.currentBase ? 0x1B5E20 : 0x000000,
        press_color: 0x333333,
        radius: px(8),
        click_func: () => {
          this.selectBase(base, baseNames[index])
        }
      })
      this.state.widgets.baseOptions[base].setProperty(prop.VISIBLE, false)
    })

    // 取消按钮
    this.state.widgets.cancelButton = createWidget(widget.BUTTON, {
      x: px(190),
      y: px(310),
      w: px(100),
      h: px(20),
      text: '取消',
      text_size: px(14),
      color: 0x888888,
      normal_color: 0x000000,
      press_color: 0x333333,
      radius: px(8),
      click_func: () => {
        this.hideBaseSelector()
      }
    })
    this.state.widgets.cancelButton.setProperty(prop.VISIBLE, false)
  },

  // 创建模式指示器
  createModeIndicator() {
    // 模式指示器背景
    this.state.widgets.modeIndicatorBg = createWidget(widget.FILL_RECT, {
      x: px(415),
      y: px(8),
      w: px(60),
      h: px(28),
      radius: px(12),
      color: 0x1B5E20
    })

    this.state.widgets.modeIndicator = createWidget(widget.TEXT, {
      x: px(420),
      y: px(10),
      w: px(50),
      h: px(24),
      text: '小写',
      text_size: px(12),
      color: 0x4CAF50,
      align_h: align.CENTER_H,
      align_v: align.CENTER_V
    })
  },

  // 输入数字/字母
  inputDigit(digit) {
    if (this.state.currentInput === '0') {
      this.state.currentInput = digit
    } else {
      this.state.currentInput += digit
    }
    this.updateDisplay()
    this.convertAll()
  },

  // 删除最后一位
  deleteDigit() {
    if (this.state.currentInput.length > 1) {
      this.state.currentInput = this.state.currentInput.slice(0, -1)
    } else {
      this.state.currentInput = '0'
    }
    this.updateDisplay()
    this.convertAll()
  },

  // 清空输入
  clearInput() {
    this.state.currentInput = '0'
    this.updateDisplay()
    this.convertAll()
  },

  // 更新显示
  updateDisplay() {
    // 更新输入显示
    this.state.widgets.inputDisplay.setProperty(prop.TEXT, this.state.currentInput)

    // 更新当前进制显示
    this.state.widgets.currentBase.setProperty(prop.TEXT, `当前: ${this.state.currentBaseName} (${this.state.currentBase})`)
  },

  // 切换键盘展开/收起
  toggleKeyboard() {
    this.state.keyboardExpanded = !this.state.keyboardExpanded

    const offsetY = this.state.keyboardExpanded ? -260 : 0 // 420 - 160 = 260

    if (this.state.keyboardExpanded) {
      // 展开键盘 - 向上移动到160px
      this.state.widgets.keyboardContainer.setProperty(prop.Y, px(160))
      this.state.widgets.keyboardHandleArea.setProperty(prop.Y, px(160))
      this.state.widgets.keyboardHandle.setProperty(prop.Y, px(178))
      // 隐藏提示文字
      this.state.widgets.keyboardHint.setProperty(prop.VISIBLE, false)
    } else {
      // 收起键盘 - 向下移动到420px
      this.state.widgets.keyboardContainer.setProperty(prop.Y, px(420))
      this.state.widgets.keyboardHandleArea.setProperty(prop.Y, px(420))
      this.state.widgets.keyboardHandle.setProperty(prop.Y, px(438))
      // 显示提示文字
      this.state.widgets.keyboardHint.setProperty(prop.VISIBLE, true)
    }

    // 移动所有键盘按键
    this.moveKeyboardKeys(offsetY)
  },

  // 移动键盘按键位置
  moveKeyboardKeys(offsetY) {
    const baseY = this.state.keyboardExpanded ? 160 : 420 // 键盘容器的Y位置

    // 数字键 (相对键盘容器的位置: 60px)
    for (let i = 0; i <= 9; i++) {
      if (this.state.widgets[`key_${i}`]) {
        this.state.widgets[`key_${i}`].setProperty(prop.Y, px(baseY + 60))
      }
    }

    // 删除键
    if (this.state.widgets.deleteKey) {
      this.state.widgets.deleteKey.setProperty(prop.Y, px(baseY + 60))
    }

    // 字母键 - 第一行 (相对键盘容器的位置: 110px)
    const row1 = ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P']
    row1.forEach(letter => {
      if (this.state.widgets[`key_${letter}`]) {
        this.state.widgets[`key_${letter}`].setProperty(prop.Y, px(baseY + 110))
      }
    })

    // 字母键 - 第二行 (相对键盘容器的位置: 160px)
    const row2 = ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L']
    row2.forEach(letter => {
      if (this.state.widgets[`key_${letter}`]) {
        this.state.widgets[`key_${letter}`].setProperty(prop.Y, px(baseY + 160))
      }
    })

    // 字母键 - 第三行 + 大小写键 (相对键盘容器的位置: 210px)
    const row3 = ['Z', 'X', 'C', 'V', 'B', 'N', 'M']
    row3.forEach(letter => {
      if (this.state.widgets[`key_${letter}`]) {
        this.state.widgets[`key_${letter}`].setProperty(prop.Y, px(baseY + 210))
      }
    })

    if (this.state.widgets.capsKey) {
      this.state.widgets.capsKey.setProperty(prop.Y, px(baseY + 210))
    }

    // 功能键 (相对键盘容器的位置: 260px)
    if (this.state.widgets.baseSelectorKey) {
      this.state.widgets.baseSelectorKey.setProperty(prop.Y, px(baseY + 260))
    }

    if (this.state.widgets.clearKey) {
      this.state.widgets.clearKey.setProperty(prop.Y, px(baseY + 260))
    }
  },

  // 切换大小写
  toggleCapsLock() {
    this.state.capsLock = !this.state.capsLock

    // 更新模式指示器
    this.state.widgets.modeIndicator.setProperty(prop.TEXT, this.state.capsLock ? '大写' : '小写')

    // 更新大小写切换按钮
    this.state.widgets.capsKey.setProperty(prop.COLOR, this.state.capsLock ? 0x000000 : 0xffffff)
    this.state.widgets.capsKey.setProperty(prop.NORMAL_COLOR, this.state.capsLock ? 0xFFC107 : 0x996600)

    // 更新所有字母按键显示
    const letters = ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', 'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z', 'X', 'C', 'V', 'B', 'N', 'M']
    letters.forEach(letter => {
      if (this.state.widgets[`key_${letter}`]) {
        const displayText = this.state.capsLock ? letter : letter.toLowerCase()
        this.state.widgets[`key_${letter}`].setProperty(prop.TEXT, displayText)
      }
    })
  },

  // 显示进制选择器
  showBaseSelector() {
    this.state.widgets.selectorBg.setProperty(prop.VISIBLE, true)
    this.state.widgets.selectorTitle.setProperty(prop.VISIBLE, true)
    this.state.widgets.cancelButton.setProperty(prop.VISIBLE, true)

    const bases = [2, 8, 10, 16, 36, 62]
    bases.forEach(base => {
      this.state.widgets.baseOptions[base].setProperty(prop.VISIBLE, true)
    })
  },

  // 隐藏进制选择器
  hideBaseSelector() {
    this.state.widgets.selectorBg.setProperty(prop.VISIBLE, false)
    this.state.widgets.selectorTitle.setProperty(prop.VISIBLE, false)
    this.state.widgets.cancelButton.setProperty(prop.VISIBLE, false)

    const bases = [2, 8, 10, 16, 36, 62]
    bases.forEach(base => {
      this.state.widgets.baseOptions[base].setProperty(prop.VISIBLE, false)
    })
  },

  // 选择进制
  selectBase(base, name) {
    this.state.currentBase = base
    this.state.currentBaseName = name

    // 更新当前进制显示
    this.state.widgets.currentBase.setProperty(prop.TEXT, `当前: ${name} (${base})`)

    // 更新选择器中的选中状态
    const bases = [2, 8, 10, 16, 36, 62]
    const baseNames = ['二进制', '八进制', '十进制', '十六进制', '三十六进制', '六十二进制']

    bases.forEach((b, index) => {
      const isSelected = b === base
      const text = `${isSelected ? '●' : '○'} ${baseNames[index]} (${b})`
      this.state.widgets.baseOptions[b].setProperty(prop.TEXT, text)
      this.state.widgets.baseOptions[b].setProperty(prop.COLOR, isSelected ? 0x4CAF50 : 0xffffff)
      this.state.widgets.baseOptions[b].setProperty(prop.NORMAL_COLOR, isSelected ? 0x1B5E20 : 0x000000)
    })

    this.hideBaseSelector()
    this.convertAll()
  },

  // 转换为十进制
  toDecimal(value, fromBase) {
    let result = 0
    for (let i = 0; i < value.length; i++) {
      const digit = CHARS.indexOf(value[i].toLowerCase())
      if (digit >= fromBase) return NaN
      result = result * fromBase + digit
    }
    return result
  },

  // 从十进制转换
  fromDecimal(decimal, toBase) {
    if (decimal === 0) return '0'
    let result = ''
    while (decimal > 0) {
      result = CHARS[decimal % toBase] + result
      decimal = Math.floor(decimal / toBase)
    }
    return result
  },

  // 转换所有进制
  convertAll() {
    const decimal = this.toDecimal(this.state.currentInput, this.state.currentBase)
    
    if (isNaN(decimal)) {
      showToast({
        content: '输入无效'
      })
      return
    }

    const bases = [2, 8, 10, 16, 36, 62]
    bases.forEach(base => {
      const result = this.fromDecimal(decimal, base)
      const displayResult = result.toUpperCase()
      this.state.widgets[`result_${base}`].setProperty(prop.TEXT, displayResult)
    })
  }
})
