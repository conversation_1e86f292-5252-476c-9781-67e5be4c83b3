# getSettings

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

获取系统显示相关信息。

## 类型

```ts
function getSettings(): Result
```

## 参数

### Result

| 属性    | 类型                    | 必填 | 默认值 | 说明         | API_LEVEL |
| ------- | ----------------------- | ---- | ------ | ------------ | --------- |
| screen  | <code>ScreenObj</code>  | 是   | -      | 屏幕状态     | 3.0       |
| wrist   | <code>WristObj</code>   | 是   | -      | 抬腕亮屏设置 | 3.0       |
| standby | <code>StandbyObj</code> | 是   | -      | 息屏显示设置 | 3.0       |

### ScreenObj

| 属性     | 类型                | 必填 | 默认值 | 说明                               | API_LEVEL |
| -------- | ------------------- | ---- | ------ | ---------------------------------- | --------- |
| status   | <code>number</code> | 是   | -      | 当前屏幕状态，`1`: 亮屏、`2`: 息屏 | 3.0       |
| duration | <code>number</code> | 是   | -      | 屏幕亮屏时长，单位秒               | 3.0       |

### WristObj

| 属性      | 类型                | 必填 | 默认值 | 说明                                    | API_LEVEL |
| --------- | ------------------- | ---- | ------ | --------------------------------------- | --------- |
| speed     | <code>number</code> | 是   | -      | 抬腕亮屏响应速度                        | 3.0       |
| model     | <code>number</code> | 是   | -      | 抬腕亮屏开启模式，值见 `model`          | 3.0       |
| startTime | <code>number</code> | 是   | -      | 抬腕亮屏开启时间，基于当天 0 点的分钟数 | 3.0       |
| endTime   | <code>number</code> | 是   | -      | 抬腕亮屏结束时间，基于当天 0 点的分钟数 | 3.0       |

### StandbyObj

| 属性      | 类型                | 必填 | 默认值 | 说明                                           | API_LEVEL |
| --------- | ------------------- | ---- | ------ | ---------------------------------------------- | --------- |
| style     | <code>number</code> | 是   | -      | 息屏表盘样式，`0`: 系统默认、`1`: 跟随当前表盘 | 3.0       |
| model     | <code>number</code> | 是   | -      | 息屏显示开启模式，值见 model                   | 3.0       |
| startTime | <code>number</code> | 是   | -      | 息屏显示开启时间，基于当天 0 点的分钟数        | 3.0       |
| endTime   | <code>number</code> | 是   | -      | 息屏显示结束时间，基于当天 0 点的分钟数        | 3.0       |

### mode

| 值  | 类型                | 说明     | API_LEVEL |
| --- | ------------------- | -------- | --------- |
| 0   | <code>number</code> | 关闭     | 3.0       |
| 1   | <code>number</code> | 定时开启 | 3.0       |
| 2   | <code>number</code> | 全天开启 | 3.0       |
| 3   | <code>number</code> | 智能开启 | 3.0       |

## 代码示例

```js
import { getSettings } from '@zos/display'

console.log(getSettings())
```
