let e=null;e="undefined"!=typeof __$$R$$__?__$$R$$__:()=>{},s()?hmApp.getPackageInfo:n()&&e("@zos/app").getPackageInfo,s()?hmUI:n()&&e("@zos/ui"),s()?hmSetting.getDeviceInfo:n()&&e("@zos/device").getDeviceInfo,s()?"undefined"!=typeof __$$app$$__&&__$$app$$__:n()&&e("@zos/i18n").getText,s()?hmApp.gotoPage:n()&&e("@zos/router").push;let t=null;function s(){return o()&&i()}function n(){return o()&&r()}function i(){return"undefined"!=typeof hmApp}function r(){return"undefined"!=typeof __$$R$$__}function o(){return i()||r()}s()?t=hmBle:n()&&(t=e("@zos/ble"));let a=null;s()?a=DeviceRuntimeCore.HmLogger:n()?a=e("@zos/utils").log:"undefined"!=typeof messaging&&"undefined"!=typeof Logger&&(a=Logger);class h{constructor(){this.listeners=new Map}on(e,t){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t)}off(e,t){if(e)if(t){const s=this.listeners.get(e);if(!s)return;const n=s.findIndex((e=>e===t));n>=0&&s.splice(n,1)}else this.listeners.delete(e)}emit(e,...t){for(let s of this.listeners.get(e)??[])s&&s(...t)}clear(){this.listeners.clear()}once(e,t){const s=(...n)=>{this.off(e,s),t(...n)};this.on(e,s)}count(e){return(this.listeners.get(e)??[]).length}}const d=setTimeout,u=clearTimeout;function c(){const e={canceled:!1};return e.promise=new Promise((function(t,s){e.resolve=t,e.reject=s})),e.cancel=()=>{e.canceled=!0,e.reject(new Error("Task canceled"))},e}function l(e,t){const s=c(),n=d((()=>{u(n),t?t&&t(s.resolve,s.reject):s.reject("Timed out in "+e+"ms.")}),e=e||1e3);return s.promise}function p(e){return t=function(e){return JSON.stringify(e)}(e),Buffer.from(t,"utf-8");var t}function f(e){return t=g(e),JSON.parse(t);var t}function g(e){return e.toString("utf-8")}function y(e){return t=function(e){return Buffer.from(e)}(e),t.toString("hex");var t}const m=o()?a.getLogger("device-message"):a.getLogger("side-message");function I(e){switch(e.toLowerCase()){case"json":return 2;case"text":default:return 1;case"bin":return 3;case"empty":return 0}}let b=1e4;function S(){return b++}let L=1e3;function k(){return L++}class T extends h{constructor(e,t,s){super(),this.id=e,this.type=t,this.ctx=s,this.tempBuf=null,this.chunks=[],this.count=0,this.finishChunk=null}addChunk(e){if(1===e.opCode&&(this.count=e.seqId,this.finishChunk=e),e.payloadLength!==e.payload.byteLength)return m.error("receive chunk data length error, expect %d but %d",e.payloadLength,e.payload.byteLength),void this.emit("error",Error(`receive chunk data length error, expect ${e.payloadLength} but ${e.payload.byteLength}`));this.chunks.push(e),this.checkIfReceiveAllChunks()}checkIfReceiveAllChunks(){if(this.count===this.chunks.length){for(let e=1;e<=this.count;e++){const t=this.chunks.find((t=>t.seqId===e));if(!t)return this.releaseBuf(),void this.emit("error",Error("receive data error"));const s=t.payload;this.tempBuf=this.tempBuf?Buffer.concat([this.tempBuf,s]):s}if(this.finishChunk){if(this.finishChunk.payload=this.tempBuf,this.finishChunk.payloadLength=this.finishChunk.payload.byteLength,this.finishChunk.totalLength!==this.finishChunk.payloadLength)return m.error("receive full data length error, expect %d but %d",this.finishChunk.payloadLength,this.finishChunk.payload.byteLength),void this.emit("error",Error(`receive full data length error, expect ${this.finishChunk.payloadLength} but ${this.finishChunk.payload.byteLength}`));this.emit("data",this.finishChunk)}}}getLength(){return this.tempBufLength}releaseBuf(){this.tempBuf=null,this.chunks=[],this.finishChunk=null,this.count=0}}class v{constructor(){this.sessions=new Map}key(e){return`${e.id}:${e.type}`}newSession(e,t,s){const n=new T(e,t,s);return this.sessions.set(this.key(n),n),n}destroy(e){e.releaseBuf(),this.sessions.delete(this.key(e))}has(e,t){return this.sessions.has(this.key({id:e,type:t}))}getById(e,t){return this.sessions.get(this.key({id:e,type:t}))}clear(){this.sessions.clear()}}class w extends Error{constructor(e,t){super(t),this.code=e}}const B=a.getLogger("message-builder"),C=new class extends h{constructor({appId:e=0,appDevicePort:s=20,appSidePort:n=0,ble:i=(o()?t:void 0)}={appId:0,appDevicePort:20,appSidePort:0,ble:o()?t:void 0}){super(),this.isDevice=o(),this.isSide=!this.isDevice,this.appId=e,this.appDevicePort=s,this.appSidePort=n,this.ble=i,this.sendMsg=this.getSafeSend(),this.chunkSize=3584,this.tempBuf=null,this.handlers=new Map,this.shakeTask=null,this.waitingShakePromise=null,this.shakeStatus=1,this.shakeTimer=0,this.sessionMgr=new v,this.on("response",(e=>{this.onResponse(e)}))}fork(e=5e3){return 2===this.shakeStatus||(this.shakeTask=c(),this.waitingShakePromise=this.shakeTask.promise,this.shakeStatus=1,this.clearShakeTimer(),this.shakeTimer=d((()=>{this.shakeStatus=4,this.shakeTask.reject(new w(1,"shake timeout"))}),e),this.shakeStatus=2,this.sendShake()),this.waitingShakePromise}clearShakeTimer(){this.shakeTimer&&u(this.shakeTimer),this.shakeTimer=0}getMessageSize(){return 3600}getMessagePayloadSize(){return 3584}getMessageHeaderSize(){return 16}buf2Json(e){return f(e)}json2Buf(e){return p(e)}now(e=Date.now()){return function(e=Date.now()){return e%1e7}(e)}connect(e){this.on("message",(e=>{this.onMessage(e)})),this.ble&&this.ble.createConnect(((e,t,s)=>{m.warn("[RAW] [R] receive index=>%d size=>%d bin=>%s",e,s,y(t)),this.onFragmentData(t)})),e&&e(this)}disConnect(e){m.debug("app ble disconnect"),this.sendClose(),this.off("message"),this.handlers.clear(),this.ble&&this.ble.disConnect(),e&&e(this)}listen(e){this.appSidePort=globalThis.getApp().port2,messaging&&messaging.peerSocket.addListener("message",(e=>{m.warn("[RAW] [R] receive size=>%d bin=>%s",e.byteLength,y(e)),this.onMessage(e)})),this.waitingShakePromise=Promise.resolve(),e&&e(this)}buildBin(e){if(e.payload.byteLength>this.chunkSize)throw new Error(`${e.payload.byteLength} greater than max size of ${this.chunkSize}`);const t=this.getMessageHeaderSize()+e.payload.byteLength;let s=Buffer.alloc(t),n=0;return s.writeUInt8(e.flag,n),n+=1,s.writeUInt8(e.version,n),n+=1,s.writeUInt16LE(e.type,n),n+=2,s.writeUInt16LE(e.port1,n),n+=2,s.writeUInt16LE(e.port2,n),n+=2,s.writeUInt32LE(e.appId,n),n+=4,s.writeUInt32LE(e.extra,n),n+=4,s.fill(e.payload,n,e.payload.byteLength+n),s}buildShake(){return this.buildBin({flag:1,version:1,type:1,port1:this.appDevicePort,port2:this.appSidePort,appId:this.appId,extra:0,payload:Buffer.from([this.appId])})}sendShake(){m.info("shake send");const e=this.buildShake();this.sendMsg(e)}buildClose(){return this.buildBin({flag:1,version:1,type:2,port1:this.appDevicePort,port2:this.appSidePort,appId:this.appId,extra:0,payload:Buffer.from([this.appId])})}sendClose(){m.info("close send");const e=this.buildClose();this.sendMsg(e)}readBin(e){const t=Buffer.from(e);let s=0;const n=t.readUInt8(s);s+=1;const i=t.readUInt8(s);s+=1;const r=t.readUInt16LE(s);s+=2;const o=t.readUInt16LE(s);s+=2;const a=t.readUInt16LE(s);s+=2;const h=t.readUInt32LE(s);s+=4;const d=t.readUInt32LE(s);return s+=4,{flag:n,version:i,type:r,port1:o,port2:a,appId:h,extra:d,payload:t.subarray(s)}}buildData(e,t={}){return this.buildBin({flag:1,version:1,type:4,port1:this.appDevicePort,port2:this.appSidePort,appId:this.appId,extra:0,...t,payload:e})}sendBin(e,t=!0){if(t&&m.warn("[RAW] [S] send size=%d bin=%s",e.byteLength,y(e.buffer)),!this.ble.send(e.buffer,e.byteLength))throw Error("send message error")}sendBinBySide(e,t=!0){t&&m.warn("[RAW] [S] send size=%d bin=%s",e.byteLength,y(e.buffer)),messaging.peerSocket.send(e.buffer)}getSafeSend(){return this.isDevice?this.sendBin.bind(this):this.sendBinBySide.bind(this)}sendHmProtocol({requestId:e,dataBin:t,type:s,contentType:n,dataType:i},{messageType:r=4}={}){const o=3518,a=t.byteLength;let h=0;const d=Buffer.alloc(o),u=e||S(),c=k();let l=1;const p=Math.ceil(a/o);function f(){return l++}for(let e=1;e<=p;e++){if(this.errorIfBleDisconnect(),e===p){const e=a-h,o=Buffer.alloc(0+e);t.copy(o,0,h,h+e),h+=e,this.sendDataWithSession({traceId:u,spanId:c,seqId:f(),payload:o,type:s,opCode:1,totalLength:a,contentType:n,dataType:i},{messageType:r});break}t.copy(d,0,h,h+o),h+=o,this.sendDataWithSession({traceId:u,spanId:c,seqId:f(),payload:d,type:s,opCode:0,totalLength:a,contentType:n,dataType:i},{messageType:r})}h===a?m.debug("HmProtocol send ok msgSize=> %d dataSize=> %d",h,a):m.error("HmProtocol send error msgSize=> %d dataSize=> %d",h,a)}sendJson({requestId:e=0,json:t,type:s=1,contentType:n,dataType:i}){const r=p(t),o=e||S();this.sendHmProtocol({requestId:o,dataBin:r,type:s,contentType:n,dataType:i})}sendBuf({requestId:e=0,buf:t,type:s=1,contentType:n,dataType:i}){const r=e||S();return this.sendHmProtocol({requestId:r,dataBin:t,type:s,contentType:n,dataType:i})}sendDataWithSession({traceId:e,spanId:t,seqId:s,payload:n,type:i,opCode:r,totalLength:o,contentType:a,dataType:h},{messageType:d}){const u=this.buildPayload({traceId:e,spanId:t,seqId:s,totalLength:o,type:i,opCode:r,payload:n,contentType:a,dataType:h});let c=this.isDevice?this.buildData(u,{type:d}):u;this.sendMsg(c)}buildPayload(e){const t=66+e.payload.byteLength;let s=Buffer.alloc(t),n=0;return s.writeUInt32LE(e.traceId,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(e.spanId,n),n+=4,s.writeUInt32LE(e.seqId,n),n+=4,s.writeUInt32LE(e.totalLength,n),n+=4,s.writeUInt32LE(e.payload.byteLength,n),n+=4,s.writeUInt8(e.type,n),n+=1,s.writeUInt8(e.opCode,n),n+=1,s.writeUInt32LE(this.now(),n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.writeUInt8(e.contentType,n),n+=1,s.writeUInt8(e.dataType,n),n+=1,s.writeUInt16LE(0,n),n+=2,s.writeUInt32LE(0,n),n+=4,s.writeUInt32LE(0,n),n+=4,s.fill(e.payload,n,e.payload.byteLength+n),s}readPayload(e){const t=Buffer.from(e);let s=0;const n=t.readUInt32LE(s);s+=4;const i=t.readUInt32LE(s);s+=4;const r=t.readUInt32LE(s);s+=4;const o=t.readUInt32LE(s);s+=4;const a=t.readUInt32LE(s);s+=4;const h=t.readUInt32LE(s);s+=4;const d=t.readUInt8(s);s+=1;const u=t.readUInt8(s);s+=1;const c=t.readUInt32LE(s);s+=4;const l=t.readUInt32LE(s);s+=4;const p=t.readUInt32LE(s);s+=4;const f=t.readUInt32LE(s);s+=4;const g=t.readUInt32LE(s);s+=4;const y=t.readUInt32LE(s);s+=4;const m=t.readUInt32LE(s);s+=4;const I=t.readUInt8(s);s+=1;const b=t.readUInt8(s);s+=1;const S=t.readUInt16LE(s);s+=2;const L=t.readUInt32LE(s);s+=4;const k=t.readUInt32LE(s);return s+=4,{traceId:n,parentId:i,spanId:r,seqId:o,totalLength:a,payloadLength:h,payloadType:d,opCode:u,contentType:I,dataType:b,timestamp1:c,timestamp2:l,timestamp3:p,timestamp4:f,timestamp5:g,timestamp6:y,timestamp7:m,extra1:S,extra2:L,extra3:k,payload:t.subarray(s)}}onFragmentData(e){const t=this.readBin(e);this.emit("raw",e),m.debug("receive data=>",JSON.stringify(t)),1===t.flag&&1===t.type?(this.appSidePort=t.port2,m.debug("shake success appSidePort=>",t.port2),this.emit("shake:response",t),this.clearShakeTimer(),this.shakeTask.resolve(),this.shakeStatus=3):1===t.flag&&4===t.type||1===t.flag&&5===t.type?(this.emit("message",t.payload),this.emit("read",t)):1===t.flag&&6===t.type?this.emit("log",t.payload):0===t.flag?m.debug("receive runtime => flag %d type %d",t.flag,t.type):1===t.flag&&2===t.type?(this.appSidePort=0,m.debug("receive close =>",this.appSidePort)):m.error("error appSidePort=>%d data=>%j",this.appSidePort,t)}errorIfBleDisconnect(){if(o()&&!this.ble.connectStatus())throw new w(2,"ble disconnect")}errorIfSideServiceDisconnect(){if(o&&!this.appSidePort)throw new w(3,"side service is not running")}getRequestCount(){return this.handlers.size}onResponse(e){const t=this.handlers.get(e.traceId);t&&t(e)}onMessage(e){const t=this.readPayload(e);let s=this.sessionMgr.getById(t.traceId,t.payloadType);s||(s=this.sessionMgr.newSession(t.traceId,t.payloadType,this),s.on("data",(e=>{1===e.opCode&&(1===e.payloadType?this.emit("request",{request:e,response:({data:t})=>{this.response({requestId:e.traceId,contentType:e.contentType,dataType:e.dataType,data:t})}}):2===e.payloadType?this.emit("response",e):3===e.payloadType&&this.emit("call",e),this.emit("data",e),this.sessionMgr.destroy(s))})),s.on("error",(e=>{this.sessionMgr.destroy(s),this.emit("error",e)}))),s.addChunk(t)}request(e,t){try{this.errorIfBleDisconnect()}catch(e){return Promise.reject(e)}return this.waitingShakePromise.then((()=>{this.errorIfBleDisconnect(),this.errorIfSideServiceDisconnect();const s=S(),n=c();t=Object.assign({timeout:6e4,contentType:"json",dataType:"json"},t),this.handlers.set(s,(({traceId:t,payload:i,dataType:r})=>{let o;switch(this.errorIfBleDisconnect(),this.errorIfSideServiceDisconnect(),m.debug("traceId=>%d payload=>%s",t,i.toString("hex")),r){case 1:o=g(i);break;case 3:default:o=i;break;case 2:o=f(i)}m.debug("request id=>%d payload=>%j",s,e),m.debug("response id=>%d payload=>%j",s,o),n.resolve(o)})),Buffer.isBuffer(e)?this.sendBuf({requestId:s,buf:e,type:1,contentType:3,dataType:I(t.dataType)}):e instanceof ArrayBuffer||ArrayBuffer.isView(e)?this.sendBuf({requestId:s,buf:Buffer.from(e),type:1,contentType:3,dataType:I(t.dataType)}):this.sendJson({requestId:s,json:e,type:1,contentType:2,dataType:I(t.dataType)});let i=!1;return Promise.race([l(t.timeout,((n,r)=>{if(i)return n();m.error(`request timeout in ${t.timeout}ms error=> %d data=> %j`,s,e),r(new w(4,`request timed out in ${t.timeout}ms.`))})),n.promise.finally((()=>{i=!0}))]).catch((e=>{throw m.error("error %j",e),e})).finally((()=>{this.handlers.delete(s)}))}))}response({requestId:e,contentType:t,dataType:s,data:n}){3===s?this.sendBuf({requestId:e,buf:n,type:2,contentType:t,dataType:s}):this.sendJson({requestId:e,json:n,type:2,contentType:t,dataType:s})}call(e){return this.waitingShakePromise.then((()=>Buffer.isBuffer(e)?this.sendBuf({buf:e,type:3,contentType:3,dataType:0}):e instanceof ArrayBuffer||ArrayBuffer.isView(e)?this.sendBuf({buf:Buffer.from(e),type:3,contentType:3,dataType:0}):this.sendJson({json:e,type:3,contentType:2,dataType:0})))}},q=function(e){return{shakeTimeout:5e3,requestTimeout:6e4,onCall(t){return t?(e.on("call",(({payload:s})=>{const n=e.buf2Json(s);t&&t(n)})),this):this},offOnCall(t){return e.off("call",t),this},call(t){return o()&&e.fork(this.shakeTimeout),e.call({jsonrpc:"hmrpcv1",...t})},onRequest(t){return t?(e.on("request",(s=>{const n=e.buf2Json(s.request.payload);t&&t(n,((e,t)=>e?s.response({data:{error:e}}):s.response({data:{result:t}})))})),this):this},cancelAllRequest(){return e.off("response"),this},offOnRequest(t){return e.off("request",t),this},request(t){return o()&&e.fork(this.shakeTimeout),B.debug("current request count=>%d",e.getRequestCount()),e.request({jsonrpc:"hmrpcv1",...t},{timeout:this.requestTimeout}).then((({error:e,result:t})=>{if(e)throw e;return t}))},connect(){return e.connect((()=>{B.debug("DeviceApp messageBuilder connect with SideService")})),this},disConnect(){return this.cancelAllRequest(),this.offOnRequest(),this.offOnCall(),e.disConnect((()=>{B.debug("DeviceApp messageBuilder disconnect SideService")})),this},start(){return e.listen((()=>{B.debug("SideService messageBuilder start to listen to DeviceApp")})),this},stop(){return this.cancelAllRequest(),this.offOnRequest(),this.offOnCall(),e.disConnect((()=>{B.debug("SideService messageBuilder stop to listen to DeviceApp")})),this}}}(C),U={onChange(e){return e?(settings.settingsStorage.addListener("change",e),this):this},offChange(){return settings.settingsStorage.removeListener("change"),this},getItem:e=>settings.settingsStorage.getItem(e),setItem:(e,t)=>settings.settingsStorage.setItem(e,t),clear:()=>settings.settingsStorage.clear(),removeItem(e){settings.settingsStorage.removeItem(e)},getAll:()=>settings.settingsStorage.toObject()},E=(R=transferFile,{onFile(e){return e?(void 0===R||R.inbox.on("newfile",(function(){const t=R.inbox.getNextFile();e&&e(t)})),this):this},onSideServiceFileFinished(e){return e?(void 0===R||R.inbox.on("file",(function(){const t=R.inbox.getNextFile();e&&e(t)})),this):this},emitFile(){return R.inbox.emit("file"),this},offFile(){return void 0===R||(R.inbox.off("newfile"),R.inbox.off("file")),this},getFile:()=>void 0===R?null:R.inbox.getNextFile(),sendFile(e,t){if(void 0===R)throw new Error("fileTransfer is not available");return R.outbox.enqueueFile(e,t)}});var R;const P=Logger.getLogger(sideService.appInfo.app.appName);function _({state:e={},onInit:t,onRun:s,onDestroy:n,...i}={}){return{state:e,...i,onInit(e){this._onCall=this.onCall?.bind(this),this._onRequest=this.onRequest?.bind(this),q.onCall(this._onCall).onRequest(this.__onRequest.bind(this)),this._onReceivedFile=this.onReceivedFile?.bind(this),E.onSideServiceFileFinished(this._onReceivedFile),this._onSettingsChange=this.onSettingsChange?.bind(this),U.onChange(this._onSettingsChange),q.start(),t?.apply(this,e),Object.entries(i).forEach((([t,s])=>{"string"==typeof t&&t.startsWith("onInit")&&s.apply(this,e)})),"undefined"!=typeof sideService&&(P.log("sideService start launchArgs=>",sideService.launchArgs),sideService.launchReasons.settingsChanged&&this._onSettingsChange(sideService.launchArgs),sideService.launchReasons.fileTransfer&&E.emitFile())},onRun(e){s?.apply(this,e),Object.entries(i).forEach((([t,s])=>{"string"==typeof t&&t.startsWith("onRun")&&s.apply(this,e)}))},onDestroy(e){this._onCall&&q.offOnCall(this._onCall),this._onRequest&&q.offOnRequest(this._onRequest),q.stop(),this._onReceivedFile&&E.offFile(this._onReceivedFile),this._onSettingsChange&&U.offChange(this._onSettingsChange),Object.entries(i).forEach((([t,s])=>{"string"==typeof t&&t.startsWith("onDestroy")&&s.apply(this,e)})),n?.apply(this,e)},request:e=>q.request(e),call:e=>q.call(e),fetch:e=>fetch(function(e){const t={timeout:1e4,...e};return t.url=new URL(e.url,t.baseURL).toString(),t}(e)),sendFile:(e,t)=>E.sendFile(e,t),download:(e,t={})=>((e,t)=>network.downloader.downloadFile({url:e,...t}))(e,t),__onRequest(e,t){return"http.request"===e.method?this.httpRequestHandler(e,t):this._onRequest(e,t)},httpRequestHandler(e,t){return this.fetch(e.params).then((e=>{t(null,{status:e.status,statusText:e.statusText,headers:e.headers,body:e.body})})).catch((e=>t({code:1,message:e.message})))}}}const x={convert:e=>image.convert(e)};export{_ as BaseSideService,x as convertLib,U as settingsLib};
