# mstOnPrepare

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册 prepare 操作回调函数。

## 类型

```ts
function mstOnPrepare(callback: Callback): Result
```

## 参数

### Callback

| 类型                                                        | 说明                      |
| ----------------------------------------------------------- | ------------------------- |
| <code>(profile: Profile, status: Status) =&#62; void</code> | 监听 prepare 事件回调函数 |

### Profile

| 类型                | 说明         |
| ------------------- | ------------ |
| <code>number</code> | Profile 指针 |

### Status

| 类型                | 说明               |
| ------------------- | ------------------ |
| <code>number</code> | 状态，`0` 表示成功 |

### Result

| 类型                 | 说明                                            |
| -------------------- | ----------------------------------------------- |
| <code>boolean</code> | 函数调用结果，`true` 表示成功、`false` 表示失败 |

## 代码示例

```js
import { mstOnPrepare } from '@zos/ble'

// ...
```
