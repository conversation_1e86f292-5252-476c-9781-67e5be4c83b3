---
title: showToast(option)
sidebar_label: showToast
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

![show_toast](/img/api/show_toast.jpg)

显示 Toast，支持 `\n` 文本换行。

## 类型

```ts
(option: Option) => void
```

## 参数

### Option: object

| 属性 | 说明           | 类型     |
| ---- | -------------- | -------- |
| text | 文本内容 | `string` |

## 代码示例

```js
import { showToast } from '@zos/ui'

Page({
  build() {
    showToast({
      text: 'Hello\nZepp OS'
    })
  }
})
```
