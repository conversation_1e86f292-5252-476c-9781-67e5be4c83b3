---
sidebar_position: 1
sidebar_label: 设计自查表
---

# 设计自查表

自查表举例了在系统设计过程中应遵守的设计规则，这将使系统的一致性体验得到提升，减少用户对交互视觉产生的困惑和失误。

自检表分为“必须”与“推荐”两个项目。推荐类请考虑自身产品定义，进行适量的修改。

| **维度** | **项目**       | **要求**                                                     | **度量** |
| -------- | -------------- | ------------------------------------------------------------ | -------- |
| 结构     | 右滑返回       | 避免使用和右滑返回冲突的交互。如受到制约可商议修改交互或更改热区。 | 必选     |
| 视觉     | 色彩           | 关键、警示、控件、基础图形等颜色是否已引用组件库预设。       | 必选     |
|          | 字体           | 字体字号是已否引用组件库。                                   | 必选     |
|          | 图标           | 图标四周预留2px空白透明的安全区域。<br/>图标输出资源为 PNG 格式，空白区域保持透明。 | 必选     |
|          |                | 应用程序图标设计风格跟随系统应用图标。                       | 推荐     |
|          | 插图           | 区分不同类型插图使用在不同页面中。                           | 必选     |
| 模板     | 弹窗           | 是否已引用组件。<br/>弹窗对齐方式请区分圆/方屏设备。             | 必选     |
|          | 空页面         | 区分有操作和无操作场景进行区别。                             | 必选     |
|          | 卡片           | 卡片高度不超过一屏。<br/>卡片四周与内容的间距为16px。<br/>右滑删除使用统一组件。 | 必选     |
|          | 列表           | 列表与列表之间的间距为8px。<br/>注意说明文字底部是否与下一个元素留出间距。 | 必选     |
| 元素     | 图表           | 根据数据的不同选择合适的图表样式。<br/>注意图表数据缺省状态。    | 推荐     |
|          | 加载           | 是否已引用系统控件库对应控件。                               | 必选     |
|          | 页面指示器     | 是否已引用系统控件库对应控件。                               | 必选     |
|          | 按钮           | 是否根据按钮交互行为层级选用恰当的按钮样式。<br/>按钮文案是否准确简练。<br/>方屏、圆屏设备按钮是否调用自对应组件库。 | 必选     |
|          | 开关           | 开关设计布局是否统一在页面右侧。                             | 必选     |
|          | 选择与状态标示 | 控件设计布局是否统一在页面右侧。                             | 必选     |
|          | 滑块           | 是否已引用系统控件库对应控件。                               | 必选     |
|          | 选择器         | 单位列数不得超过5列。<br/>双位列数不得超过3列。<br/>三位列数不得超过2列。 | 必选     |
|          | 数字输入       | 是否已引用系统控件库对应控件。                               | 必选     |
|          | 快捷回复       | 是否已引用系统控件库对应控件。                               | 必选     |
|          | 文本           | 是否引用了组件。<br/>颜色是否引用了样式。<br/>规定好文本框的大小。   | 必选     |
|          | 进度           | 是否已引用了组件                                             | 必选     |
| 国际化   | 文本           | 英文、俄文、德文长度较长，需考虑多行文字同时出现时的处理方式。 | 必选     |
| 无障碍   | 色彩           | 提示信息是否有文字。<br/>使用颜色仅需区分信息时避免色盲难识别的颜色同时出现。<br/>颜色对比度自查。 | 推荐     |