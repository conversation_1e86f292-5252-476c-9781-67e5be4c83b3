# Balance手表进制转换器

一个专为Balance智能手表设计的进制转换器应用，基于Zepp OS 3.0开发。

## ✨ 功能特色

### 🎯 核心功能
- **多进制支持**：支持2、8、10、16、36、62进制转换
- **实时转换**：输入时自动转换所有支持的进制
- **智能输入验证**：自动检测无效输入并提示用户

### 🎨 界面设计
- **480x480圆屏适配**：完美适配Balance手表的圆形屏幕
- **深色主题**：护眼的深色界面，绿色主题色
- **半透明毛玻璃效果**：现代化的视觉设计
- **响应式布局**：适配不同的交互状态

### ⌨️ 交互体验
- **可收纳键盘**：点击展开/收起，节省屏幕空间
- **完整QWERTY布局**：标准键盘布局，支持所有字符输入
- **大小写切换**：支持大写/小写字母输入
- **进制选择器**：弹窗式进制选择，操作简便
- **模式指示器**：实时显示当前输入模式

## 🏗️ 项目结构

```
base-converter/
├── app.js                 # 应用入口
├── app.json               # 应用配置
├── package.json           # 项目配置
├── page/
│   └── gt/
│       └── converter/
│           └── index.page.js    # 主页面逻辑
├── assets/                # 资源文件
├── test/
│   └── converter-test.js  # 功能测试
└── README.md             # 项目说明
```

## 🚀 快速开始

### 环境要求
- Zepp OS 开发环境
- Node.js (用于测试)
- Balance手表或模拟器

### 安装步骤
1. 克隆项目到本地
2. 使用Zepp OS开发工具打开项目
3. 编译并部署到设备

### 测试
```bash
# 运行功能测试
node test/converter-test.js
```

## 🎮 使用说明

### 基本操作
1. **输入数字**：点击数字键盘输入数值
2. **输入字母**：点击字母键盘输入字母（支持大小写）
3. **选择进制**：点击输入区域或"进制选择"按钮选择输入进制
4. **查看结果**：所有支持的进制转换结果实时显示

### 键盘操作
- **展开键盘**：点击底部键盘区域向上拖拽
- **收起键盘**：再次点击键盘区域向下收起
- **大小写切换**：点击"↑"按钮切换大小写模式
- **删除**：点击"⌫"删除最后一位
- **清空**：点击"清空"清除所有输入

### 进制选择
- 点击输入区域或"进制选择"按钮
- 在弹出的选择器中选择目标进制
- 支持：二进制(2)、八进制(8)、十进制(10)、十六进制(16)、三十六进制(36)、六十二进制(62)

## 🔧 技术实现

### 核心算法
- **进制转换算法**：基于字符集映射的通用进制转换
- **输入验证**：实时验证输入字符是否符合当前进制要求
- **字符集支持**：`0-9a-zA-Z` 共62个字符，支持最高62进制

### UI组件
- **createWidget API**：使用Zepp OS原生UI组件
- **响应式布局**：基于px()函数的像素适配
- **状态管理**：Page实例状态管理模式

### 性能优化
- **按需渲染**：键盘和选择器按需显示/隐藏
- **高效转换**：优化的进制转换算法
- **内存管理**：合理的组件生命周期管理

## 🎨 设计理念

这个应用完全复刻了Web原型的设计理念：
- **圆屏优先**：专为圆形屏幕设计的布局
- **可收纳交互**：节省屏幕空间的交互设计
- **视觉一致性**：保持与原型一致的视觉风格
- **用户友好**：简单直观的操作流程

## 📱 兼容性

- **Zepp OS版本**：3.0+
- **设备支持**：Balance手表及其他480x480圆屏设备
- **API兼容**：使用稳定的Zepp OS API

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License
