# mstOnDescWriteComplete

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册 Descriptor 数据写入完成回调函数。

## 类型

```ts
function mstOnDescWriteComplete(callback: Callback): Result
```

## 参数

### Callback

| 类型                                                                                        | 说明                            |
| ------------------------------------------------------------------------------------------- | ------------------------------- |
| <code>(profile: Profile, uuid: UUID, descUUID: DescUUID, status: Status) =&#62; void</code> | Descriptor 数据写入完成回调函数 |

### Profile

| 类型                | 说明         |
| ------------------- | ------------ |
| <code>number</code> | Profile 指针 |

### UUID

| 类型                | 说明                       |
| ------------------- | -------------------------- |
| <code>string</code> | Characteristic UUID 字符串 |

### DescUUID

| 类型                | 说明                   |
| ------------------- | ---------------------- |
| <code>string</code> | Descriptor UUID 字符串 |

### Status

| 类型                | 说明               |
| ------------------- | ------------------ |
| <code>number</code> | 状态，`0` 表示成功 |

### Result

| 类型                 | 说明                                            |
| -------------------- | ----------------------------------------------- |
| <code>boolean</code> | 函数调用结果，`true` 表示成功、`false` 表示失败 |

## 代码示例

```js
import { mstOnDescWriteComplete } from '@zos/ble'

// ...
```
