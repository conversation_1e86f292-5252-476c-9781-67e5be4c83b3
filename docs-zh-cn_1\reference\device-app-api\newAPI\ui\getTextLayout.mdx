---
title: getTextLayout(text_string, options)
sidebar_label: getTextLayout
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

计算出目标文本布局完成之后的高度和宽度，并不会实际进行渲染，只进行布局计算。

可用于计算固定宽度下多行文本排布的高度，或者单行文本排布的宽度。

## 类型

```ts
(text: string, options: object) => result
```

## 参数

| 参数        | 说明                 | 必填 | 类型      |
| ----------- | -------------------- | ---- | --------- |
| text | 待计算布局的文本内容 | 是   | `string`  |
| options     | 支持传入的选项参数   | 是   | `Options` |

### Options

| 属性       | 说明                                                                                                | 必填 | 类型     | API_LEVEL |
| ---------- | --------------------------------------------------------------------------------------------------- | ---- | -------- | --------- |
| text_size  | 文字大小                                                                                            | 是   | `number` | 2.0       |
| text_width | 单行文本的宽度，如果设置 `wrapped：0`，`text_width` 需要传 `0`                                      | 是   | `number` | 2.0       |
| wrapped    | 文本是否换行，`0`：不换行；`1`: 换行                                                                | 否   | `number` | 2.0       |
| rows_max   | 限制最大行数（当所给文本超过限制最大行数时，会被截断并且在后面补上省略号）。 默认值为 `0`，即不限制 | 否   | `number` | 3.0       |

### result: object

| 属性   | 说明       | 类型     | API_LEVEL |
| ------ | ---------- | -------- | --------- |
| width  | 宽度像素值 | `number` | 2.0       |
| height | 高度像素值 | `number` | 2.0       |
| rows | 文本显示行数，当 `wrapped` 字段为 `false`，`rows` 的值为 `1` | `number` | 2.0       |
| result | 计算结果， `-1` - 出错，`0` - 成功，`1` - 成功，字符被截断并加了省略号 | `number` | 2.0       |
| text | 计算成功时，返回截断并且加了省略号的文本内容，可用于实际 UI 控件的显示 | `string` | 2.0       |

## 代码示例

```js
import { getTextLayout } from '@zos/ui'

const { width, height } = getTextLayout('turn right and go alone the road', {
  text_size: 30,
  text_width: 200
})

console.log('width', width)
console.log('height', height)
```

```js
import { getTextLayout } from '@zos/ui'

const { width, height } = getTextLayout('turn right and go alone the road', {
  text_size: 30,
  text_width: 0,
  wrapped: 0
})

console.log('width', width)
console.log('height', height)
```
