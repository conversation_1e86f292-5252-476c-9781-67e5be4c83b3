---
title: 社区共建
sidebar_label: 社区共建
---

## 前言

Zepp OS 的成长，离不开社区开发者的支持和帮助。

Zepp OS 团队始终坚持开放的态度，积极吸取社区建议，持续为开发者提供高自由度、功能更强大系统能力 API，与此同时，听取批评和意见，不断优化性能与系统稳定性。

两年多的时间内，广大开发者用强大的编码能力，凝聚智慧与创意为 Zepp OS 生态贡献了非常多高质量的作品，服务广大 Zepp OS 用户，持续创造价值。

Zepp OS 团队举办多场开发者活动与比赛，给予积极参与的开发者丰厚的物质奖励。

Zepp OS 欢迎开发者参与到社区共建中，与 Zepp OS 互相成就，共同成长。

下文中列举了常见的参与到 Zepp OS 建设的方式。

## 贡献形式

### 参与社区讨论

在 [Zepp OS 开发者社区](../community.md) 中的对应板块反馈 bug、提新功能建议、寻求帮助等。

Github 讨论区不断积累社区开发者的讨论，这对整个社区是非常大的帮助。

### 内容分享

关于 Zepp OS 完整的小程序、表盘作品，或是工具库，可以联系我们进行投稿，收录到 [awesome-zeppos](https://github.com/zepp-health/awesome-zeppos) 仓库中。

相关博客文章，或者视频资料，我们会收录到 Github 讨论区的 [Blog](https://github.com/orgs/zepp-health/discussions/categories/blog) 栏目下，并在技术社区协助宣传。

### 参与开发者活动

Zepp OS 团队不定期会举办活动，如每月一次的 workshop 活动，或者开发者比赛等。

活动会提前在开发者官网和 Zepp OS 开发者社区进行公告，对积极参与的开发者，给予丰厚的物质奖励。

### 修改文档

开发者可以参照 [文档建设](./document-construction.mdx) 中的指引来修改开发者文档。
