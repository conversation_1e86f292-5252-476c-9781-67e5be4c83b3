---
title: 文档建设
sidebar_label: 文档建设
---

## 介绍

开发者可以依照本文指引参与到文档建设，修复文档错误，或者贡献新内容。

开发者文档项目仓库在 Github，官方团队会定期处理 Pull Request，并且进行文档部署。

## 如何修改

Github 提供多种项目的修改方式，本文中会介绍在线编辑和本地修改两种方式，以及各自适用的场景。

## 在线编辑

对于开发者在阅读过程中发现的明显错误，可以点击文档下方的「编辑此页」，跳转至 Github 的在线编辑页面，对文档修改完成后，提交 Pull Request，等待官方团队处理。

## 本地修改

参考文档项目 [zeppos-docs https://github.com/zepp-health/zeppos-docs](https://github.com/zepp-health/zeppos-docs) Readme 中的项目起步章节，将项目 clone 至本地后修改。
