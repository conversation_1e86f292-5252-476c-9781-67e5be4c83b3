---
title: Zepp OS 版本说明
sidebar_label: Zepp OS 版本说明
---

## 文档版本说明

Zepp OS 文档版本 `v3+` 包括了 `API_LEVEL` 3.0 及以上版本的内容，点击文档平台左上角版本切换按钮可以查看早期 Zepp OS 版本的文档。

![version_change](/img/version_change.gif)

:::info
`API_LEVEL` 是 Zepp OS 的开放能力版本号，从 Zepp OS 2.0 开始引入。它定义了应用可以使用的系统功能范围，包括系统接口、UI 控件等。`API_LEVEL` 版本号越高，可用的功能就越丰富。

每个搭载 Zepp OS 的设备都有对应的 `API_LEVEL` 版本，新款设备通常支持更高的 `API_LEVEL`。系统会保证高版本 `API_LEVEL` 完全兼容低版本的应用。

完整 `API_LEVEL` 和兼容性相关内容请参考 [《API_LEVEL（兼容性）》](../guides/framework/device/compatibility.md)。

查看搭载 Zepp OS 的设备的 `API_LEVEL` 版本请参考 [搭载 Zepp OS 设备列表](../reference/related-resources/device-list.mdx)。
:::

:::tip
如果开发者想为搭载 Zepp OS 1.0 的设备开发应用，请查看 v1.0 版本的文档。

想为搭载 Zepp OS 2.x 及以上的版本的设备开发应用，直接查看最新版本的文档。
:::

## 版本信息

- [API_LEVEL 4.0 特性](../guides/version-info/new-features-40.md)
- [Zepp OS 3.0 特性](../guides/version-info/new-features-30.md)
- [Zepp OS 2.1 特性](../guides/version-info/new-features-21.md)
- [Zepp OS 2.0 特性](../guides/version-info/new-features.md)
