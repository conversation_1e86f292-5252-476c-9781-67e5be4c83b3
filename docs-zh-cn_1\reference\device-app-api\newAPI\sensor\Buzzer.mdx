---
title: Buzzer
sidebar_label: Buzzer 蜂鸣器
---

> API_LEVEL `3.6` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

蜂鸣器。

## 方法

### isEnabled

获取系统蜂鸣器场景设置中的其他选项是否开启，设置 -> 声音与震动 -> 蜂鸣场景 -> 其他

```ts
isEnabled(): boolean
```

### getSourceType

获取蜂鸣模式

```ts
getSourceType(): Type
```

#### Type

| 值       | 类型                | 说明   | API_LEVEL |
| -------- | ------------------- | ------ | --------- |
| ALARM    | <code>number</code> | 闹钟   | 3.6       |
| REMIND_1 | <code>number</code> | 提醒 1 | 3.6       |
| REMIND_2 | <code>number</code> | 提醒 2 | 3.6       |
| OPERATE  | <code>number</code> | 操作   | 3.6       |
| SUCCESS  | <code>number</code> | 成功   | 3.6       |
| FAILURE  | <code>number</code> | 失败   | 3.6       |

### getStrength

获取蜂鸣器强度，`0` - weak，`1` - medium，`2` - high

```ts
getStrength(): number
```

### start

开始蜂鸣，可以传入 `type` 指定系统内置蜂鸣模式，`repeatCount` 为重复次数，默认 `0`，不重复

```ts
start(type: number, repeatCount: 0): void
```

### stop

停止蜂鸣器

```ts
stop(): void
```

## 代码示例

```js
import { createWidget, widget, prop, align, text_style } from '@zos/ui'
import { Buzzer } from '@zos/sensor'
import { px } from '@zos/utils'

const sceneList = ['ALARM', 'REMIND_1', 'REMIND_2', 'OPERATE', 'SUCCESS', 'FAILURE']

Page({
  state: {
    pageName: 'BUZZER',
    currentIndex: 0,
  },
  build() {
    const buzzer = new Buzzer()
    const sceneText = createWidget(widget.TEXT, {
      x: px(0),
      y: px(120),
      w: px(480),
      h: px(46),
      color: 0xffffff,
      text_size: px(20),
      align_h: align.CENTER_H,
      align_v: align.CENTER_V,
      text_style: text_style.NONE,
      text: `${sceneList[this.state.currentIndex]}`,
    })

    const startBuzzer = () => {
      const alarmType = buzzer.getSourceType()[sceneList[this.state.currentIndex]]

      if (buzzer.isEnabled()) {
        buzzer.start(alarmType)
      }

      this.state.currentIndex = (this.state.currentIndex + 1) % sceneList.length
      sceneText.setProperty(prop.MORE, {
        text: `BUZZER: ${sceneList[this.state.currentIndex]}`,
      })
    }

    createWidget(widget.BUTTON, {
      x: px(80),
      y: px(300),
      w: px(300),
      h: px(60),
      radius: px(12),
      normal_color: 0xfc6950,
      press_color: 0xfeb4a8,
      text: 'START BUZZER',
      click_func: startBuzzer,
    })
  },
})
```
