# mstStartScan

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

扫描并发现蓝牙外围设备，可以根据 filter 条件进行过滤。

## 类型

```ts
function mstStartScan(callback: Callback, filter?: Filter): Result
```

## 参数

### Callback

| 类型                                                                              | 说明                 |
| --------------------------------------------------------------------------------- | -------------------- |
| <code>(result: ScanResult, filter?: Filter, timeout?: Timeout) =&#62; void</code> | 接收扫描结果回调函数 |

### ScanResult

| 属性               | 类型                                    | 说明                                                 | API_LEVEL |
| ------------------ | --------------------------------------- | ---------------------------------------------------- | --------- |
| dev_name           | <code>string</code>                     | 设备名称                                             | 3.0       |
| dev_addr           | <code>ArrayBuffer</code>                | 设备 MAC 地址，长度 6 字节，建议使用 Uint8Array 视图 | 3.0       |
| rssi               | <code>number</code>                     | RSSI 信号强度                                        | 3.0       |
| service_uuid_array | <code>Array&#60;string&#62;</code>      | 广播数据中的 Service UUID 数组                       | 3.0       |
| service_data_array | <code>Array&#60;ServiceData&#62;</code> | 广播数据中的 Service 数据对象数组                    | 3.0       |

### ServiceData

| 属性         | 类型                     | 说明         | API_LEVEL |
| ------------ | ------------------------ | ------------ | --------- |
| uuid         | <code>string</code>      | Service UUID | 3.0       |
| service_data | <code>ArrayBuffer</code> | Service 数据 | 3.0       |

### Filter

| 属性              | 类型                | 必填 | 默认值 | 说明                         | API_LEVEL |
| ----------------- | ------------------- | ---- | ------ | ---------------------------- | --------- |
| device_name       | <code>string</code> | 否   | -      | 设备名称                     | 3.0       |
| fuzzy_mode        | <code>string</code> | 否   | -      | 设备名称匹配是否采用模糊模式 | 3.0       |
| service_uuid      | <code>string</code> | 否   | -      | Service UUID                 | 3.0       |
| service_data_uuid | <code>string</code> | 否   | -      | Service 数据 UUID            | 3.0       |
| manufacturer_id   | <code>number</code> | 否   | -      | 设备商 ID                    | 3.0       |

### Timeout

| 属性       | 类型                        | 必填 | 默认值 | 说明                                               | API_LEVEL |
| ---------- | --------------------------- | ---- | ------ | -------------------------------------------------- | --------- |
| duration   | <code>number</code>         | 否   | -      | 扫描持续时长，单位秒。到给定的时长后，扫描自动停止 | 3.0       |
| on_timeout | <code>() =&#62; void</code> | 否   | -      | 扫描停止后的回调函数                               | 3.0       |

### Result

| 类型                 | 说明                                            |
| -------------------- | ----------------------------------------------- |
| <code>boolean</code> | 函数调用结果，`true` 表示成功、`false` 表示失败 |

## 代码示例

```js
import { mstStartScan } from '@zos/ble'

// ...
```
