import { px } from '@zos/utils'

// Balance手表屏幕适配配置
export const SCREEN_CONFIG = {
  width: 480,
  height: 480,
  centerX: 240,
  centerY: 240,
  padding: 20
}

// 布局配置
export const LAYOUT = {
  // 顶部区域
  top: {
    titleBar: {
      x: px(20),
      y: px(20),
      w: px(440),
      h: px(48),
      radius: px(24)
    },
    inputSection: {
      x: px(20),
      y: px(84),
      w: px(440),
      h: px(96),
      radius: px(16)
    }
  },

  // 结果显示区域
  results: {
    background: {
      x: px(20),
      y: px(200),
      w: px(440),
      h: px(200),
      radius: px(16)
    },
    itemHeight: px(30),
    startY: px(220)
  },

  // 键盘区域
  keyboard: {
    background: {
      x: px(0),
      y: px(420),
      w: px(480),
      h: px(320),
      radius: px(20)
    },
    collapsed: {
      y: px(420)
    },
    expanded: {
      y: px(160)
    },
    handle: {
      x: px(220),
      y: px(438),
      w: px(40),
      h: px(4)
    },
    keys: {
      numbers: {
        startX: px(20),
        y: px(500),
        width: px(36),
        height: px(40),
        gap: px(4)
      },
      letters: {
        row1: {
          startX: px(20),
          y: px(550),
          width: px(40),
          height: px(40),
          gap: px(4)
        },
        row2: {
          startX: px(42),
          y: px(600),
          width: px(40),
          height: px(40),
          gap: px(4)
        },
        row3: {
          startX: px(80),
          y: px(650),
          width: px(40),
          height: px(40),
          gap: px(4)
        }
      },
      functions: {
        y: px(700),
        height: px(40)
      }
    }
  },

  // 进制选择器
  selector: {
    background: {
      x: px(90),
      y: px(140),
      w: px(300),
      h: px(200),
      radius: px(16)
    },
    title: {
      x: px(90),
      y: px(150),
      w: px(300),
      h: px(30)
    },
    options: {
      startX: px(100),
      startY: px(190),
      width: px(280),
      height: px(20),
      gap: px(5)
    }
  },

  // 模式指示器
  modeIndicator: {
    background: {
      x: px(415),
      y: px(8),
      w: px(60),
      h: px(28),
      radius: px(12)
    },
    text: {
      x: px(420),
      y: px(10),
      w: px(50),
      h: px(24)
    }
  }
}

// 颜色配置
export const COLORS = {
  background: 0x1a1a1a,
  cardBackground: 0x0d0d0d,
  inputBackground: 0x141414,
  keyboardBackground: 0x000000,
  
  primary: 0x4CAF50,
  primaryDark: 0x1B5E20,
  
  text: {
    primary: 0xffffff,
    secondary: 0x888888,
    accent: 0x4CAF50
  },
  
  button: {
    normal: 0x1a1a1a,
    pressed: 0x333333,
    delete: 0xf44336,
    deletePressed: 0xff6659,
    caps: 0x996600,
    capsActive: 0xFFC107,
    special: 0x4CAF50,
    specialPressed: 0x66BB6A
  },
  
  handle: 0x4d4d4d
}

// 字体配置
export const FONTS = {
  title: px(18),
  input: px(24),
  base: px(14),
  result: {
    label: px(14),
    value: px(16)
  },
  key: px(16),
  keySmall: px(14),
  selector: {
    title: px(16),
    option: px(14)
  },
  indicator: px(12)
}
