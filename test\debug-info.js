// 调试信息输出
console.log('🔧 Balance手表进制转换器 - 调试信息')
console.log('=====================================')

// 模拟页面状态
const mockState = {
  currentInput: '123',
  currentBase: 10,
  currentBaseName: '十进制',
  capsLock: false,
  keyboardExpanded: false
}

console.log('📱 当前状态:')
console.log(`输入值: ${mockState.currentInput}`)
console.log(`当前进制: ${mockState.currentBaseName} (${mockState.currentBase})`)
console.log(`大小写模式: ${mockState.capsLock ? '大写' : '小写'}`)
console.log(`键盘状态: ${mockState.keyboardExpanded ? '展开' : '收起'}`)

// 模拟键盘位置计算
function calculateKeyboardPositions(expanded) {
  const baseY = expanded ? 160 : 420
  
  return {
    container: baseY,
    handle: baseY + 18,
    numbers: baseY + 60,
    row1: baseY + 110,
    row2: baseY + 160,
    row3: baseY + 210,
    functions: baseY + 260
  }
}

console.log('\n⌨️ 键盘位置计算:')
console.log('收起状态:', calculateKeyboardPositions(false))
console.log('展开状态:', calculateKeyboardPositions(true))

// 模拟进制转换
const CHARS = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'

function toDecimal(value, fromBase) {
  let result = 0
  for (let i = 0; i < value.length; i++) {
    const digit = CHARS.indexOf(value[i].toLowerCase())
    if (digit >= fromBase) return NaN
    result = result * fromBase + digit
  }
  return result
}

function fromDecimal(decimal, toBase) {
  if (decimal === 0) return '0'
  let result = ''
  while (decimal > 0) {
    result = CHARS[decimal % toBase] + result
    decimal = Math.floor(decimal / toBase)
  }
  return result
}

console.log('\n🔢 转换测试:')
const testInput = mockState.currentInput
const decimal = toDecimal(testInput, mockState.currentBase)
console.log(`输入: ${testInput} (${mockState.currentBase}进制)`)
console.log(`十进制: ${decimal}`)

if (!isNaN(decimal)) {
  const bases = [2, 8, 10, 16, 36, 62]
  const baseNames = ['二进制', '八进制', '十进制', '十六进制', '三十六进制', '六十二进制']
  
  bases.forEach((base, index) => {
    const result = fromDecimal(decimal, base)
    console.log(`${baseNames[index]}: ${result.toUpperCase()}`)
  })
}

console.log('\n✅ 调试信息输出完成')

// 模拟UI更新
console.log('\n🎨 UI更新模拟:')
console.log('inputDisplay.setText:', mockState.currentInput)
console.log('currentBase.setText:', `当前: ${mockState.currentBaseName} (${mockState.currentBase})`)
console.log('modeIndicator.setText:', mockState.capsLock ? '大写' : '小写')

// 检查常见问题
console.log('\n🚨 常见问题检查:')
console.log('1. 输入显示问题:', mockState.currentInput ? '✅ 正常' : '❌ 空值')
console.log('2. 进制显示问题:', mockState.currentBaseName ? '✅ 正常' : '❌ 未设置')
console.log('3. 转换结果问题:', !isNaN(decimal) ? '✅ 正常' : '❌ 转换失败')
console.log('4. 键盘位置问题:', '需要在实际设备上测试')
