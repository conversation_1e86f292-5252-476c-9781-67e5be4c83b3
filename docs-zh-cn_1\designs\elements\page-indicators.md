---
sidebar_label: 页面指示器
---

# 页面指示器

## **滚动条**

滚动条是在纵向长页面滚动时，用来指示页面当前所处位置的控件。

![Design](/img/design/5857111c5dab65a6d6723275392fdf9a.png)

### 使用规则

- 界面内容超出显示区，进入页面时固定位置显示滚动条，若无任何操作，2秒后消失（触控屏幕或使用表冠交互可再次调用显示）。
- 背景条➁为固定长度，动态条➀长度随内容长度动态变化，内容高度越长，动态条高度越短。

![Design](/img/design/34bc7e326c3cabafe37a910f409158a1.png)

![Design](/img/design/fa18fdf6cb0c0a8b9d1cd93987022842.png)

## **横向分页导航**

分页导航点用于显示横向页面数量及当前的所处位置。

### 使用规则

- 横向存在多页面时，顶部显示导航点。
- 导航点与横切屏幕数量一一对应，用户通过左右滑动或旋转表冠切换至对应页面。

![Design](/img/design/9968a05d97a56e47ca3bdc7665afdb7c.png)
