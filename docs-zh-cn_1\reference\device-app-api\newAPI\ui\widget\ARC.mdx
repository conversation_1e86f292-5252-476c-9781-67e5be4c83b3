---
title: ARC
sidebar_label: ARC 圆弧
---

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../../guides/framework/device/compatibility.md)。

![arc_sample](/img/api/arc_sample.jpg)

圆弧控件展示圆弧进度。支持设置线宽、颜色、开始和结束的角度。

## 创建 UI 控件

```js
import { createWidget, widget } from '@zos/ui'

const arc = createWidget(widget.ARC, Param)
```

## 类型

### Param: object

| 属性        | 说明                          | 是否必须 | 类型     |
| ----------- | ----------------------------- | -------- | -------- |
| x           | 控件 x 坐标                   | 是       | `number` |
| y           | 控件 y 坐标                   | 是       | `number` |
| w           | 控件显示宽度                  | 是       | `number` |
| h           | 控件显示高度                  | 是       | `number` |
| start_angle | 开始角度（3 点钟方向为 0 度） | 否       | `number` |
| end_angle   | 结束角度（3 点钟方向为 0 度） | 否       | `number` |
| line_width  | 线宽                          | 是       | `number` |
| color       | 颜色                          | 是       | `number` |

:::info
`ARC` 控件会在以 x,y 坐标为左上角的宽 w 高 h 的矩形边界内绘制椭圆，再以给定角度切割成圆弧
:::

## 属性访问支持列表

| 属性名       | setProperty | getProperty | [setter](../gettersetter.mdx) | [getter](../gettersetter.mdx) |
|--------------|-------------|-------------|-------------------------------|-------------------------------|
| x           | Y           | Y           | Y                             | Y                             |
| y           | Y           | Y           | Y                             | Y                             |
| w           | Y           | Y           | Y                             | Y                             |
| h           | Y           | Y           | Y                             | Y                             |
| start_angle | Y           | Y           | Y                             | Y                             |
| end_angle   | Y           | Y           | Y                             | Y                             |
| line_width  | Y           | Y           | Y                             | Y                             |
| color       | Y           | Y           | Y                             | Y                             |

## 代码示例

```js
import { createWidget, widget, event, prop } from '@zos/ui'

Page({
  build() {
    const arc = createWidget(widget.ARC, {
      x: 100,
      y: 100,
      w: 250,
      h: 250,
      start_angle: -90,
      end_angle: 90,
      color: 0xfc6950,
      line_width: 20
    })

    arc.addEventListener(event.CLICK_DOWN, (info) => {
      arc.setProperty(prop.MORE, {
        y: 150
      })
    })
  }
})
```
