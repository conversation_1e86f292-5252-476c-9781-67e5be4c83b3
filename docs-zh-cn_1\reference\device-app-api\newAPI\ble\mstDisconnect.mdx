# mstDisconnect

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

断开设备连接。

## 类型

```ts
function mstDisconnect(connectId: ConnectId): Result
```

## 参数

### ConnectId

| 类型                | 说明                                          |
| ------------------- | --------------------------------------------- |
| <code>number</code> | 使用 `mstConnect` API 连接成功时返回的连接 ID |

### Result

| 类型                 | 说明                                            |
| -------------------- | ----------------------------------------------- |
| <code>boolean</code> | 函数调用结果，`true` 表示成功、`false` 表示失败 |

## 代码示例

```js
import { mstDisconnect } from '@zos/ble'

// ...
```
