---
title: 应用图标常见问题
---

## FAQ

### Q1: 对于应用程序图标的颜色有什么要求？

应用图标应避免使用纯黑色或颜色过深的背景。因为系统界面的默认背景是黑色，深色背景会使图标与系统背景融为一体，从而降低图标的辨识度。我们建议选用应用的品牌色或其他能够体现应用特性的色彩。

![img](/img/docs/guides/faq/icon/1.png)

> 纯黑色或近黑色的背景会融合在系统的默认黑色背景上，无法区分，如上图所示，这会使你的应用图标失去圆形的背景轮廓，降低图标的识别度

![img](/img/docs/guides/faq/icon/2.png)

> 正确设计示例

应避免在图标设计中包含透明区域。影响图标的视觉效果和辨识性。

![img](/img/docs/guides/faq/icon/4c309867-b0e4-4598-ae34-3c04132bc694.png)

注意图标不要带有透明度，确保背景色不会透过图标，以保持图标的清晰可辨。

![img](/img/docs/guides/faq/icon/9bf9601b-edcc-472d-ae4c-db15e57eecad.png)

### Q2: 应用程序图标的形状可以自由选择吗？

应用程序图标整体应为正圆形，避免采用异形设计。导致视觉上的不规则感，破坏系统的整体统一性。

![img](/img/docs/guides/faq/icon/ec9f9a23-fef8-493e-9cf9-939cb5e4eae4.png)

> ① 不要使用非圆形的背板

图标的主体内容不要超出圆形背板的边界。这样做有助于保持图标的整体规整性和视觉统一性。

![img](/img/docs/guides/faq/icon/0283ae8a-0836-46f2-b286-c56fc06e76c2.png)

> ① 主体内容大小不要超出背板区域

### Q3: 应用图标需要输出那些尺寸？

#### 系统中使用的图标

![img](/img/docs/guides/faq/icon/2f41283a-7818-4d16-877d-b17257f82324.png)

> ① 图标内容尺寸240×240px
>
> ② 四周各4px空白透明的安全区域
>
> ③ 应用图标最终尺寸

#### 实际使用效果示例

![img](/img/docs/guides/faq/icon/3e82fb79-38e1-4e41-ad50-4fdf7103a466.png)

> ① 多变视图中的「网易云音乐」应用程序图标
>
> ② 经典视图中的「网易云音乐」应用程序图标

#### App 商城使用的图标

此处的 icon 仅用于在管理中心上传，在应用市场展示

- icon 尺寸大小：240×240 px，图片格式：PNG
- icon 为背景透明的圆形图标，保证四周无填充

![img](/img/docs/guides/faq/icon/c607c7fd-6049-40aa-8ec5-2eca53b50f0b.png)

> ① 应用 icon
>
> ② 240 * 240 px 尺寸的方形透明背景
>
> ③ 应用 icon：应用 icon 放在方形的透明背景中间，上下左右无边距

#### 实际使用效果示例

![img](/img/docs/guides/faq/icon/841011cf-10ba-4b73-a322-3c35e7098359.png)

> ①  应用市场中的「汇率换算」应用程序图标

## 设计规范

详细的设计规范请参考 [图标设计规范](../../designs/visual/icons.md)

## 资源下载

- 应用商城小程序图标 [psd 文件](https://upload-cdn.zepp.com/zepp-applet-and-wechat-applet/20240430/42bd3495c6caa90be11487a03d59cf31.psd)
- 系统小程序图标 [psd 文件](https://upload-cdn.zepp.com/zepp-applet-and-wechat-applet/20240430/d9728ac75fa8277f567f6b772a8ced9e.psd)
