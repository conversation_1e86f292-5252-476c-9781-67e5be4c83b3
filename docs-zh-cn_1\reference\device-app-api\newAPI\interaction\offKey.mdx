# offKey

> API_LEVEL `2.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

取消 `onKey` 注册的监听按键事件。

## 类型

```ts
function offKey(): void
```

## 代码示例

```js
import { onKey, offKey, KEY_UP, KEY_EVENT_CLICK } from '@zos/interaction'

const keyCallback = (key, keyEvent) => {
  if (key === KEY_UP && keyEvent === KEY_EVENT_CLICK) {
    console.log('up click')
  }
  return true
}

onKey({
  callback: keyCallback,
})

offKey()
```
