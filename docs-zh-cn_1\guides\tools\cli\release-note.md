---
title: 版本历史
---

# 版本历史
### V1.2.2 (2022年10月27日)

#### 🔧 修复
  1. 修复windows环境创建项目报错问题

### V1.2.1 (2022年10月16日)

#### 🔧 修复
  1. 修复windows环境下部分特殊场景循环构建问题

### V1.2.0 (2022年09月23日)

#### 🚀  新增
  1. 增加了2.0版本的项目创建和开发
  2. Bridge模式支持屏幕截图命令（需配合模拟器或Zepp APP使用）

### V1.1.9 (2022年08月18日)

#### 🚀  新增
  1. 显示预览二维码过期时间

#### 🔧 修复
  1. 修复“Bridge”模式的日志输出功能引起的程序异常中断问题

---
### V1.1.8 (2022年07月21日)

#### 🚀  新增
  1. 支持新设备 GTS4 mini
  2. 默认开启js转bin功能

---

### V1.1.7 (2022年07月08日)

#### 📈  优化
  1. 优化构建产物体积

#### 🔧  修复
  1. 修复"dev"命令需要反复选择构建目标的问题

---

### V1.1.6 (2022年06月23日)

#### 📈  优化
  1. 优化构建产物的文件名
  2. 优化一些已知问题

---

### V1.1.5 (2022年06月09日)

#### 🚀  新增
  1. 支持新设备 t-rex2

#### 📈  优化
  1. 优化一些已知问题

---

### V1.1.4 (2022年06月01日)

#### 🔧  修复
  1. 修复包过大无法预览问题
  
---

### V1.1.3 (2022年05月26日)

#### 📈  优化
  1. 优化一些已知问题
  
---

### V1.1.2 (2022年05月12日)
#### 🚀  新增
  1. 执行`zeus preview`、 `zeus bridge`时，检测到未登录或token失效时自动打开登录页
  2. minor及major版本更新时进行提醒

#### 📈  优化
  1. `zeus dev` 模式下忽略非项目文件变更触发构建
  2. 优化登录成功页
  
---


### V1.1.1 (2022年04月27日)
#### 🔧  修复

1. 修复本地模式连接模拟器异常问题
  
---

### V1.1.0 (2022年04月21日)
#### 🚀  新增
  1. 新增 `zeus bridge` 命令

#### 📈  优化
  1. token 失效的语义化提醒
  
---

### V1.0.24 (2022年02月17日)
#### 🚀  新增
  1. 新增 `zeus status` 命令

#### 📈  优化
  1. 添加类型定义提醒（新创建的应用）
  
---

### V1.0.22 (2022年01月21日)
#### 🚀  新增
  1. 支持按需构建

#### 📈  优化
  1. 简化部分场景操作流程
  2. 增加构建异常提示
  
---

### V1.0.18 (2022年01月06日)

#### 🚀  新增
  1. 新增支持表盘扫码安装
  
---

### V1.0.16 (2021年12月28日)

#### 🚀  新增
  1. 新增支持表盘开发/调试/打包功能
  2. 新增支持账号退出功能

#### 📈  优化
  1. 优化开发模式稳定性
  2. 优化图片转换工具，应用包瘦身
