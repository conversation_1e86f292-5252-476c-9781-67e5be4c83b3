---
sidebar_label: 屏幕触控
---

# 屏幕触控

屏幕触控是利用手势让用户可以使用触摸与屏幕元素进行交互。人们可以通过对触摸屏施加不同程度的压力来访问其他功能。当人们触摸并按下应用程序图标时，可以打开应用程序。
## 设计原则 

- 用户通常希望以下标准手势在每个 Zepp OS 体验中表现相同。
- 避免使用标准手势执行非标准操作，重新定义标准手势的含义会增加复杂性并可能导致混淆。

## 使用规则 

**轻敲**  点击选择一个按钮或一个项目。  

**滑动**  通过手势滑动屏幕显示另一个屏幕内容，向不同方向滑动会产生不同的效果。
- 在内容区域中向下滑动可以显示上下文
- 在基于页面的应用程序中，水平滑动通常会显示下一个或上一个屏幕。
- 垂直滑动可以滚动当前屏幕内容。
- 支持双指滑动/单指滑动，响应同样事件。  

**边缘滑动**  从屏幕边缘滑动可以呼出快捷功能。  

- 在系统屏幕上，从顶部边缘向下滑动会显示控制中心。从底部边缘向上滑动会显示通知中心。
- 在应用内向右方边缘滑动回上一级。  

**用力按压或长按**  在 Zepp OS 中，用户可以触发某些功能。
- 在不同场景中响应长按触发时间应根据对象所占面积区分为短响应和长响应。

![Design](/img/design/rules-for-use.png)
>①短响应长按触发时间，例如控制中心长按触发编辑   ②长响应长按触发时间，例如更换表盘