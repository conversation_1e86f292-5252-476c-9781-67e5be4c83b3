# mstOnCharaNotification

> API_LEVEL `3.0` 开始支持，API 兼容性请参考 [API_LEVEL](../../../../guides/framework/device/compatibility.md)。

注册 Characteristic Notification 到达回调函数。

## 类型

```ts
function mstOnCharaNotification(callback: Callback): Result
```

## 参数

### Callback

| 类型                                                                                | 说明                                     |
| ----------------------------------------------------------------------------------- | ---------------------------------------- |
| <code>(profile: Profile, uuid: UUID, data: Data, length: Length) =&#62; void</code> | Characteristic Notification 到达回调函数 |

### Profile

| 类型                | 说明         |
| ------------------- | ------------ |
| <code>number</code> | Profile 指针 |

### UUID

| 类型                | 说明                       |
| ------------------- | -------------------------- |
| <code>string</code> | Characteristic UUID 字符串 |

### Data

| 类型                     | 说明                                   |
| ------------------------ | -------------------------------------- |
| <code>ArrayBuffer</code> | 读取到的数据，使用 Uint8Array 视图读取 |

### Length

| 类型                | 说明     |
| ------------------- | -------- |
| <code>number</code> | 数据长度 |

### Result

| 类型                 | 说明                                            |
| -------------------- | ----------------------------------------------- |
| <code>boolean</code> | 函数调用结果，`true` 表示成功、`false` 表示失败 |

## 代码示例

```js
import { mstOnCharaNotification } from '@zos/ble'

// ...
```
