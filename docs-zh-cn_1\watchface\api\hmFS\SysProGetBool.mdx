---
title: hmFS.SysProGetBool(key)
sidebar_label: SysProGetBool
---

获取临时存储的布尔值，系统重启将清除

## 类型

```ts
(key: string) => result
```

## 参数

### key

| 说明     | 必填 | 类型     | 默认值 |
| -------- | ---- | -------- | ------ |
| 键字符串 | 是   | `string` | -      |

### result

| 说明         | 类型      |
| ------------ | --------- |
| 存储的布尔值 | `boolean` |

## 用法

```js
const result = hmFS.SysProGetBool('test_key')
console.log(result)
```
