---
title: GRADKIENT_POLYLINE 折线图
sidebar_label: GRADKIENT_POLYLINE 折线图
---

![折线图](/img/api/polyline.png)

## 代码示例

```js
//每次set_more时会清除画布
const lineDatas = [
  { x: 0, y: 0 },
  { x: 100, y: 10 },
  { x: 200, y: 50 }
]
var widget = hmUI.createWidget(hmUI.widget.GRADKIENT_POLYLINE, {
  x: 0,
  y: 0,
  w: 480,
  h: 200,
  type: hmUI.data_type.SLEEP //如果设置type后 下方的函数即可不用 让固件来绘制数据 目前只支持HEART和SLEEP
})
//js绘制数据
widget.clear() //清除画布
widget.addLine({
  //添加线
  data: lineDatas,
  count: lineDatas.length
})
widget.addPoint({
  //添加点
  data: lineDatas,
  count: lineDatas.length
})
```
